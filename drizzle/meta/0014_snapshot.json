{"id": "b0d1ce93-c04c-432d-97a4-dc7385ce7978", "prevId": "107da99b-6175-4239-acf7-2f9442f61af4", "version": "7", "dialect": "postgresql", "tables": {"public.account_type": {"name": "account_type", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"account_type_name_key": {"name": "account_type_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.address_type": {"name": "address_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"address_type_name_key": {"name": "address_type_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.alembic_version": {"name": "alembic_version", "schema": "", "columns": {"version_num": {"name": "version_num", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.application_documents": {"name": "application_documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "application_id": {"name": "application_id", "type": "integer", "primaryKey": false, "notNull": true}, "documentType": {"name": "documentType", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "documentUrl": {"name": "documentUrl", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "uploaded_at": {"name": "uploaded_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "verificationStatus": {"name": "verificationStatus", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "verified_at": {"name": "verified_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "verified_by": {"name": "verified_by", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"application_documents_application_id_applications_id_fk": {"name": "application_documents_application_id_applications_id_fk", "tableFrom": "application_documents", "tableTo": "applications", "columnsFrom": ["application_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "application_documents_verified_by_party_id_fk": {"name": "application_documents_verified_by_party_id_fk", "tableFrom": "application_documents", "tableTo": "party", "columnsFrom": ["verified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.applications": {"name": "applications", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "type": {"name": "type", "type": "application_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "application_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "applicant_id": {"name": "applicant_id", "type": "integer", "primaryKey": false, "notNull": true}, "asset_id": {"name": "asset_id", "type": "integer", "primaryKey": false, "notNull": true}, "fraction": {"name": "fraction", "type": "numeric", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "reviewer_id": {"name": "reviewer_id", "type": "integer", "primaryKey": false, "notNull": false}, "review_notes": {"name": "review_notes", "type": "text", "primaryKey": false, "notNull": false}, "reviewed_at": {"name": "reviewed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"applications_applicant_id_party_id_fk": {"name": "applications_applicant_id_party_id_fk", "tableFrom": "applications", "tableTo": "party", "columnsFrom": ["applicant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "applications_asset_id_assets_id_fk": {"name": "applications_asset_id_assets_id_fk", "tableFrom": "applications", "tableTo": "assets", "columnsFrom": ["asset_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "applications_reviewer_id_party_id_fk": {"name": "applications_reviewer_id_party_id_fk", "tableFrom": "applications", "tableTo": "party", "columnsFrom": ["reviewer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.assets": {"name": "assets", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": false}, "updated_by": {"name": "updated_by", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"assets_created_by_party_id_fk": {"name": "assets_created_by_party_id_fk", "tableFrom": "assets", "tableTo": "party", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "assets_updated_by_party_id_fk": {"name": "assets_updated_by_party_id_fk", "tableFrom": "assets", "tableTo": "party", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.asset_ownership": {"name": "asset_ownership", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "asset_id": {"name": "asset_id", "type": "integer", "primaryKey": false, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "fraction": {"name": "fraction", "type": "numeric", "primaryKey": false, "notNull": true}, "effective_from": {"name": "effective_from", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "effective_to": {"name": "effective_to", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"asset_ownership_asset_id_assets_id_fk": {"name": "asset_ownership_asset_id_assets_id_fk", "tableFrom": "asset_ownership", "tableTo": "assets", "columnsFrom": ["asset_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "asset_ownership_party_id_party_id_fk": {"name": "asset_ownership_party_id_party_id_fk", "tableFrom": "asset_ownership", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.booking_events": {"name": "booking_events", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "booking_reference": {"name": "booking_reference", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "borrower_party_id": {"name": "borrower_party_id", "type": "integer", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "booking_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "requested_start": {"name": "requested_start", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "requested_end": {"name": "requested_end", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "confirmed_start": {"name": "confirmed_start", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "confirmed_end": {"name": "confirmed_end", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "actual_start": {"name": "actual_start", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "actual_end": {"name": "actual_end", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "changed_by": {"name": "changed_by", "type": "integer", "primaryKey": false, "notNull": true}, "approved_by": {"name": "approved_by", "type": "integer", "primaryKey": false, "notNull": false}, "quoted_price": {"name": "quoted_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "final_price": {"name": "final_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": false, "default": "'USD'"}, "purpose": {"name": "purpose", "type": "text", "primaryKey": false, "notNull": false}, "special_requirements": {"name": "special_requirements", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "change_reason": {"name": "change_reason", "type": "text", "primaryKey": false, "notNull": false}, "event_timestamp": {"name": "event_timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"booking_events_reference_idx": {"name": "booking_events_reference_idx", "columns": [{"expression": "booking_reference", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "booking_events_vehicle_id_idx": {"name": "booking_events_vehicle_id_idx", "columns": [{"expression": "vehicle_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "booking_events_borrower_party_id_idx": {"name": "booking_events_borrower_party_id_idx", "columns": [{"expression": "borrower_party_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "booking_events_status_idx": {"name": "booking_events_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "booking_events_timestamp_idx": {"name": "booking_events_timestamp_idx", "columns": [{"expression": "event_timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"booking_events_vehicle_id_vehicles_id_fk": {"name": "booking_events_vehicle_id_vehicles_id_fk", "tableFrom": "booking_events", "tableTo": "vehicles", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "booking_events_borrower_party_id_party_id_fk": {"name": "booking_events_borrower_party_id_party_id_fk", "tableFrom": "booking_events", "tableTo": "party", "columnsFrom": ["borrower_party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "booking_events_changed_by_party_id_fk": {"name": "booking_events_changed_by_party_id_fk", "tableFrom": "booking_events", "tableTo": "party", "columnsFrom": ["changed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "booking_events_approved_by_party_id_fk": {"name": "booking_events_approved_by_party_id_fk", "tableFrom": "booking_events", "tableTo": "party", "columnsFrom": ["approved_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bookings": {"name": "bookings", "schema": "", "columns": {"vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "reference": {"name": "reference", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "start_datetime": {"name": "start_datetime", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_datetime": {"name": "end_datetime", "type": "timestamp", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "bookingstatus", "typeSchema": "public", "primaryKey": false, "notNull": false}, "total_price": {"name": "total_price", "type": "double precision", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_bookings_id": {"name": "ix_bookings_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"bookings_party_id_fkey": {"name": "bookings_party_id_fkey", "tableFrom": "bookings", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "bookings_vehicle_id_fkey": {"name": "bookings_vehicle_id_fkey", "tableFrom": "bookings", "tableTo": "vehicles", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"bookings_reference_key": {"name": "bookings_reference_key", "nullsNotDistinct": false, "columns": ["reference"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.cities": {"name": "cities", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "country_id": {"name": "country_id", "type": "integer", "primaryKey": false, "notNull": true}, "province_state_id": {"name": "province_state_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {"ix_cities_id": {"name": "ix_cities_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"cities_country_id_countries_id_fk": {"name": "cities_country_id_countries_id_fk", "tableFrom": "cities", "tableTo": "countries", "columnsFrom": ["country_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "cities_province_state_id_province_state_id_fk": {"name": "cities_province_state_id_province_state_id_fk", "tableFrom": "cities", "tableTo": "province_state", "columnsFrom": ["province_state_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.company": {"name": "company", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "registration_number": {"name": "registration_number", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "country_id": {"name": "country_id", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "registration_date": {"name": "registration_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "city_id": {"name": "city_id", "type": "integer", "primaryKey": false, "notNull": false}, "purpose": {"name": "purpose", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"company_country_id_countries_id_fk": {"name": "company_country_id_countries_id_fk", "tableFrom": "company", "tableTo": "countries", "columnsFrom": ["country_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "company_city_id_fkey": {"name": "company_city_id_fkey", "tableFrom": "company", "tableTo": "cities", "columnsFrom": ["city_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "company_party_id_fkey": {"name": "company_party_id_fkey", "tableFrom": "company", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.company_notification_preferences": {"name": "company_notification_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": false, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "booking_notifications": {"name": "booking_notifications", "type": "boolean", "primaryKey": false, "notNull": false}, "payment_notifications": {"name": "payment_notifications", "type": "boolean", "primaryKey": false, "notNull": false}, "maintenance_alerts": {"name": "maintenance_alerts", "type": "boolean", "primaryKey": false, "notNull": false}, "member_activity": {"name": "member_activity", "type": "boolean", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_company_notification_preferences_id": {"name": "ix_company_notification_preferences_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"company_notification_preferences_company_id_fkey": {"name": "company_notification_preferences_company_id_fkey", "tableFrom": "company_notification_preferences", "tableTo": "company", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"company_notification_preferences_pkey": {"name": "company_notification_preferences_pkey", "columns": ["id", "company_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.company_ownership": {"name": "company_ownership", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "fraction": {"name": "fraction", "type": "numeric", "primaryKey": false, "notNull": true}, "effective_from": {"name": "effective_from", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "effective_to": {"name": "effective_to", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"company_ownership_company_id_fkey": {"name": "company_ownership_company_id_fkey", "tableFrom": "company_ownership", "tableTo": "company", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "company_ownership_party_id_fkey": {"name": "company_ownership_party_id_fkey", "tableFrom": "company_ownership", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.company_ownership_invite": {"name": "company_ownership_invite", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "fraction": {"name": "fraction", "type": "numeric", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "companyownershipinviteenum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"company_ownership_invite_company_id_fkey": {"name": "company_ownership_invite_company_id_fkey", "tableFrom": "company_ownership_invite", "tableTo": "company", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.compliance_audit": {"name": "compliance_audit", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "entityType": {"name": "entityType", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "entityId": {"name": "entityId", "type": "integer", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "actorId": {"name": "actorId", "type": "integer", "primaryKey": false, "notNull": true}, "changes": {"name": "changes", "type": "jsonb", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"audit_entity_idx": {"name": "audit_entity_idx", "columns": [{"expression": "entityType", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entityId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "audit_created_at_idx": {"name": "audit_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"compliance_audit_actorId_party_id_fk": {"name": "compliance_audit_actorId_party_id_fk", "tableFrom": "compliance_audit", "tableTo": "party", "columnsFrom": ["actorId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.compliance_document": {"name": "compliance_document", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "submissionId": {"name": "submissionId", "type": "integer", "primaryKey": false, "notNull": true}, "documentNumber": {"name": "documentNumber", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "documentType": {"name": "documentType", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "issuingAuthorityId": {"name": "issuingAuthorityId", "type": "integer", "primaryKey": false, "notNull": false}, "issue_date": {"name": "issue_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "expiry_date": {"name": "expiry_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "document_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "documentUrl": {"name": "documentUrl", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"compliance_document_submissionId_compliance_submission_id_fk": {"name": "compliance_document_submissionId_compliance_submission_id_fk", "tableFrom": "compliance_document", "tableTo": "compliance_submission", "columnsFrom": ["submissionId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "compliance_document_issuingAuthorityId_issuing_authority_id_fk": {"name": "compliance_document_issuingAuthorityId_issuing_authority_id_fk", "tableFrom": "compliance_document", "tableTo": "issuing_authority", "columnsFrom": ["issuingAuthorityId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.compliance_requirement": {"name": "compliance_requirement", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "compliance_category", "typeSchema": "public", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "requirement_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "requirement_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "validation_rules": {"name": "validation_rules", "type": "jsonb", "primaryKey": false, "notNull": false}, "document_schema": {"name": "document_schema", "type": "jsonb", "primaryKey": false, "notNull": false}, "default_validity_days": {"name": "default_validity_days", "type": "integer", "primaryKey": false, "notNull": false}, "riskLevel": {"name": "riskLevel", "type": "risk_level", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'LOW'"}, "is_recurring": {"name": "is_recurring", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "recurring_period_days": {"name": "recurring_period_days", "type": "integer", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "effective_from": {"name": "effective_from", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "effective_to": {"name": "effective_to", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.compliance_submission": {"name": "compliance_submission", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "requirement_id": {"name": "requirement_id", "type": "integer", "primaryKey": false, "notNull": true}, "entityType": {"name": "entityType", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "entityId": {"name": "entityId", "type": "integer", "primaryKey": false, "notNull": true}, "currentStatusHistoryId": {"name": "currentStatusHistoryId", "type": "integer", "primaryKey": false, "notNull": false}, "batchId": {"name": "batchId", "type": "uuid", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "validationResults": {"name": "validationResults", "type": "jsonb", "primaryKey": false, "notNull": false}, "submittedBy": {"name": "submittedBy", "type": "integer", "primaryKey": false, "notNull": true}, "submitted_at": {"name": "submitted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"submission_entity_idx": {"name": "submission_entity_idx", "columns": [{"expression": "entityType", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entityId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "current_status_compliance_submission_idx": {"name": "current_status_compliance_submission_idx", "columns": [{"expression": "currentStatusHistoryId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"compliance_submission_requirement_id_compliance_requirement_id_fk": {"name": "compliance_submission_requirement_id_compliance_requirement_id_fk", "tableFrom": "compliance_submission", "tableTo": "compliance_requirement", "columnsFrom": ["requirement_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "compliance_submission_currentStatusHistoryId_status_history_id_fk": {"name": "compliance_submission_currentStatusHistoryId_status_history_id_fk", "tableFrom": "compliance_submission", "tableTo": "status_history", "columnsFrom": ["currentStatusHistoryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "compliance_submission_submittedBy_party_id_fk": {"name": "compliance_submission_submittedBy_party_id_fk", "tableFrom": "compliance_submission", "tableTo": "party", "columnsFrom": ["submittedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.compliance_verification": {"name": "compliance_verification", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "submissionId": {"name": "submissionId", "type": "integer", "primaryKey": false, "notNull": true}, "method": {"name": "method", "type": "verification_method", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "verification_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "verifiedBy": {"name": "verifiedBy", "type": "integer", "primaryKey": false, "notNull": false}, "verificationData": {"name": "verificationData", "type": "jsonb", "primaryKey": false, "notNull": false}, "result": {"name": "result", "type": "jsonb", "primaryKey": false, "notNull": false}, "cost": {"name": "cost", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "started_at": {"name": "started_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"compliance_verification_submissionId_compliance_submission_id_fk": {"name": "compliance_verification_submissionId_compliance_submission_id_fk", "tableFrom": "compliance_verification", "tableTo": "compliance_submission", "columnsFrom": ["submissionId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "compliance_verification_verifiedBy_party_id_fk": {"name": "compliance_verification_verifiedBy_party_id_fk", "tableFrom": "compliance_verification", "tableTo": "party", "columnsFrom": ["verifiedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contact_point": {"name": "contact_point", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "contact_point_type_id": {"name": "contact_point_type_id", "type": "integer", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "address_type_id": {"name": "address_type_id", "type": "integer", "primaryKey": false, "notNull": false}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": true}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "verification_date": {"name": "verification_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "verification_method": {"name": "verification_method", "type": "text", "primaryKey": false, "notNull": false}, "mtadata": {"name": "mtadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"contact_point_address_type_id_fkey": {"name": "contact_point_address_type_id_fkey", "tableFrom": "contact_point", "tableTo": "address_type", "columnsFrom": ["address_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "contact_point_contact_point_type_id_fkey": {"name": "contact_point_contact_point_type_id_fkey", "tableFrom": "contact_point", "tableTo": "contact_point_type", "columnsFrom": ["contact_point_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "contact_point_party_id_fkey": {"name": "contact_point_party_id_fkey", "tableFrom": "contact_point", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contact_point_type": {"name": "contact_point_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "validation_pattern": {"name": "validation_pattern", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"contact_point_type_name_key": {"name": "contact_point_type_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contact_type": {"name": "contact_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"contact_type_name_key": {"name": "contact_type_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contract_status": {"name": "contract_status", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"contract_status_name_key": {"name": "contract_status_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contract_type": {"name": "contract_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"contract_type_name_key": {"name": "contract_type_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.countries": {"name": "countries", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.current_entity_status": {"name": "current_entity_status", "schema": "", "columns": {"entityType": {"name": "entityType", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "entityId": {"name": "entityId", "type": "integer", "primaryKey": false, "notNull": true}, "currentStatus": {"name": "currentStatus", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "last_transition_at": {"name": "last_transition_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "lastTransitionBy": {"name": "lastTransitionBy", "type": "integer", "primaryKey": false, "notNull": true}, "statusHistoryId": {"name": "statusHistoryId", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"current_entity_status_lastTransitionBy_party_id_fk": {"name": "current_entity_status_lastTransitionBy_party_id_fk", "tableFrom": "current_entity_status", "tableTo": "party", "columnsFrom": ["lastTransitionBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "current_entity_status_statusHistoryId_status_history_id_fk": {"name": "current_entity_status_statusHistoryId_status_history_id_fk", "tableFrom": "current_entity_status", "tableTo": "status_history", "columnsFrom": ["statusHistoryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"current_entity_status_entityType_entityId_pk": {"name": "current_entity_status_entityType_entityId_pk", "columns": ["entityType", "entityId"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.dispute_comments": {"name": "dispute_comments", "schema": "", "columns": {"comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": true}, "dispute_id": {"name": "dispute_id", "type": "integer", "primaryKey": false, "notNull": true}, "reply_to_comment_id": {"name": "reply_to_comment_id", "type": "integer", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_dispute_comments_id": {"name": "ix_dispute_comments_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"dispute_comments_dispute_id_fkey": {"name": "dispute_comments_dispute_id_fkey", "tableFrom": "dispute_comments", "tableTo": "disputes", "columnsFrom": ["dispute_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "dispute_comments_reply_to_comment_id_fkey": {"name": "dispute_comments_reply_to_comment_id_fkey", "tableFrom": "dispute_comments", "tableTo": "dispute_comments", "columnsFrom": ["reply_to_comment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.dispute_media": {"name": "dispute_media", "schema": "", "columns": {"dispute_id": {"name": "dispute_id", "type": "integer", "primaryKey": false, "notNull": true}, "media_path": {"name": "media_path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_dispute_media_id": {"name": "ix_dispute_media_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"dispute_media_dispute_id_fkey": {"name": "dispute_media_dispute_id_fkey", "tableFrom": "dispute_media", "tableTo": "disputes", "columnsFrom": ["dispute_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.disputes": {"name": "disputes", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "dispute_type": {"name": "dispute_type", "type": "disputetype", "typeSchema": "public", "primaryKey": false, "notNull": true}, "party_offending": {"name": "party_offending", "type": "integer", "primaryKey": false, "notNull": true}, "party_logging": {"name": "party_logging", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "priority", "typeSchema": "public", "primaryKey": false, "notNull": false}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "dispute_status": {"name": "dispute_status", "type": "disputestatus", "typeSchema": "public", "primaryKey": false, "notNull": false}}, "indexes": {"ix_disputes_id": {"name": "ix_disputes_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"disputes_party_offending_fkey": {"name": "disputes_party_offending_fkey", "tableFrom": "disputes", "tableTo": "party", "columnsFrom": ["party_offending"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "disputes_party_logging_fkey": {"name": "disputes_party_logging_fkey", "tableFrom": "disputes", "tableTo": "party", "columnsFrom": ["party_logging"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "disputes_company_id_fkey": {"name": "disputes_company_id_fkey", "tableFrom": "disputes", "tableTo": "company", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.document_type": {"name": "document_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"document_type_name_key": {"name": "document_type_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.emergency_contacts": {"name": "emergency_contacts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "contact_type": {"name": "contact_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"ix_emergency_contacts_id": {"name": "ix_emergency_contacts_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.group_member_roles": {"name": "group_member_roles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "group_id": {"name": "group_id", "type": "integer", "primaryKey": false, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "group_role", "primaryKey": false, "notNull": true}, "effective_from": {"name": "effective_from", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "effective_to": {"name": "effective_to", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "'infinity'::timestamp"}}, "indexes": {}, "foreignKeys": {"group_member_roles_group_id_groups_id_fk": {"name": "group_member_roles_group_id_groups_id_fk", "tableFrom": "group_member_roles", "tableTo": "groups", "columnsFrom": ["group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "group_member_roles_party_id_party_id_fk": {"name": "group_member_roles_party_id_party_id_fk", "tableFrom": "group_member_roles", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.group_membership_invitations": {"name": "group_membership_invitations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "group_id": {"name": "group_id", "type": "integer", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "group_role", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "invitation_status", "primaryKey": false, "notNull": true, "default": "'SENT'"}, "invited_by": {"name": "invited_by", "type": "integer", "primaryKey": false, "notNull": true}, "invitation_token": {"name": "invitation_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "accepted_at": {"name": "accepted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "accepted_by": {"name": "accepted_by", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"group_membership_invitations_group_id_groups_id_fk": {"name": "group_membership_invitations_group_id_groups_id_fk", "tableFrom": "group_membership_invitations", "tableTo": "groups", "columnsFrom": ["group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "group_membership_invitations_invited_by_party_id_fk": {"name": "group_membership_invitations_invited_by_party_id_fk", "tableFrom": "group_membership_invitations", "tableTo": "party", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "group_membership_invitations_accepted_by_party_id_fk": {"name": "group_membership_invitations_accepted_by_party_id_fk", "tableFrom": "group_membership_invitations", "tableTo": "party", "columnsFrom": ["accepted_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.group_memberships": {"name": "group_memberships", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "group_id": {"name": "group_id", "type": "integer", "primaryKey": false, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "effective_from": {"name": "effective_from", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "effective_to": {"name": "effective_to", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "'infinity'::timestamp"}}, "indexes": {}, "foreignKeys": {"group_memberships_group_id_groups_id_fk": {"name": "group_memberships_group_id_groups_id_fk", "tableFrom": "group_memberships", "tableTo": "groups", "columnsFrom": ["group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "group_memberships_party_id_party_id_fk": {"name": "group_memberships_party_id_party_id_fk", "tableFrom": "group_memberships", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.group_shared_vehicles": {"name": "group_shared_vehicles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "group_id": {"name": "group_id", "type": "integer", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "shared_by": {"name": "shared_by", "type": "integer", "primaryKey": false, "notNull": true}, "effective_from": {"name": "effective_from", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "effective_to": {"name": "effective_to", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "'infinity'::timestamp"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"group_shared_vehicles_group_id_groups_id_fk": {"name": "group_shared_vehicles_group_id_groups_id_fk", "tableFrom": "group_shared_vehicles", "tableTo": "groups", "columnsFrom": ["group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "group_shared_vehicles_vehicle_id_vehicles_id_fk": {"name": "group_shared_vehicles_vehicle_id_vehicles_id_fk", "tableFrom": "group_shared_vehicles", "tableTo": "vehicles", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "group_shared_vehicles_shared_by_party_id_fk": {"name": "group_shared_vehicles_shared_by_party_id_fk", "tableFrom": "group_shared_vehicles", "tableTo": "party", "columnsFrom": ["shared_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_group_vehicle": {"name": "unique_group_vehicle", "nullsNotDistinct": false, "columns": ["group_id", "vehicle_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.groups": {"name": "groups", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "city_id": {"name": "city_id", "type": "integer", "primaryKey": false, "notNull": false}, "country_id": {"name": "country_id", "type": "integer", "primaryKey": false, "notNull": false}, "initial_purpose": {"name": "initial_purpose", "type": "company_purpose", "primaryKey": false, "notNull": true}, "is_managed": {"name": "is_managed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_by": {"name": "updated_by", "type": "integer", "primaryKey": false, "notNull": false}, "creator": {"name": "creator", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"groups_party_id_party_id_fk": {"name": "groups_party_id_party_id_fk", "tableFrom": "groups", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "groups_city_id_cities_id_fk": {"name": "groups_city_id_cities_id_fk", "tableFrom": "groups", "tableTo": "cities", "columnsFrom": ["city_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "groups_country_id_countries_id_fk": {"name": "groups_country_id_countries_id_fk", "tableFrom": "groups", "tableTo": "countries", "columnsFrom": ["country_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "groups_creator_party_id_fk": {"name": "groups_creator_party_id_fk", "tableFrom": "groups", "tableTo": "party", "columnsFrom": ["creator"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.handover_issue_resolutions": {"name": "handover_issue_resolutions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "issue_id": {"name": "issue_id", "type": "integer", "primaryKey": false, "notNull": true}, "resolution": {"name": "resolution", "type": "text", "primaryKey": false, "notNull": true}, "resolved_by": {"name": "resolved_by", "type": "integer", "primaryKey": false, "notNull": true}, "resolved_at": {"name": "resolved_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"handover_issue_resolutions_issue_id_idx": {"name": "handover_issue_resolutions_issue_id_idx", "columns": [{"expression": "issue_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "handover_issue_resolutions_resolved_at_idx": {"name": "handover_issue_resolutions_resolved_at_idx", "columns": [{"expression": "resolved_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"handover_issue_resolutions_issue_id_handover_issues_id_fk": {"name": "handover_issue_resolutions_issue_id_handover_issues_id_fk", "tableFrom": "handover_issue_resolutions", "tableTo": "handover_issues", "columnsFrom": ["issue_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "handover_issue_resolutions_resolved_by_party_id_fk": {"name": "handover_issue_resolutions_resolved_by_party_id_fk", "tableFrom": "handover_issue_resolutions", "tableTo": "party", "columnsFrom": ["resolved_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.handover_issues": {"name": "handover_issues", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "handover_id": {"name": "handover_id", "type": "integer", "primaryKey": false, "notNull": true}, "reported_by": {"name": "reported_by", "type": "integer", "primaryKey": false, "notNull": true}, "issue_type": {"name": "issue_type", "type": "issue_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "reported_at": {"name": "reported_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"handover_issues_handover_id_idx": {"name": "handover_issues_handover_id_idx", "columns": [{"expression": "handover_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "handover_issues_reported_at_idx": {"name": "handover_issues_reported_at_idx", "columns": [{"expression": "reported_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"handover_issues_handover_id_vehicle_handovers_id_fk": {"name": "handover_issues_handover_id_vehicle_handovers_id_fk", "tableFrom": "handover_issues", "tableTo": "vehicle_handovers", "columnsFrom": ["handover_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "handover_issues_reported_by_party_id_fk": {"name": "handover_issues_reported_by_party_id_fk", "tableFrom": "handover_issues", "tableTo": "party", "columnsFrom": ["reported_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.handover_status_events": {"name": "handover_status_events", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "handover_id": {"name": "handover_id", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "handover_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status_timestamp": {"name": "status_timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "changed_by": {"name": "changed_by", "type": "integer", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "event_timestamp": {"name": "event_timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"handover_status_events_handover_id_idx": {"name": "handover_status_events_handover_id_idx", "columns": [{"expression": "handover_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "handover_status_events_status_idx": {"name": "handover_status_events_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "handover_status_events_timestamp_idx": {"name": "handover_status_events_timestamp_idx", "columns": [{"expression": "status_timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"handover_status_events_handover_id_vehicle_handovers_id_fk": {"name": "handover_status_events_handover_id_vehicle_handovers_id_fk", "tableFrom": "handover_status_events", "tableTo": "vehicle_handovers", "columnsFrom": ["handover_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "handover_status_events_changed_by_party_id_fk": {"name": "handover_status_events_changed_by_party_id_fk", "tableFrom": "handover_status_events", "tableTo": "party", "columnsFrom": ["changed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.identification_type": {"name": "identification_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "validation_pattern": {"name": "validation_pattern", "type": "text", "primaryKey": false, "notNull": false}, "expiration_required": {"name": "expiration_required", "type": "boolean", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"identification_type_name_key": {"name": "identification_type_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.individual": {"name": "individual", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true}, "middle_name": {"name": "middle_name", "type": "text", "primaryKey": false, "notNull": false}, "salutation": {"name": "salutation", "type": "text", "primaryKey": false, "notNull": false}, "suffix": {"name": "suffix", "type": "text", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false}, "birth_date": {"name": "birth_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "marital_status": {"name": "marital_status", "type": "text", "primaryKey": false, "notNull": false}, "nationality": {"name": "nationality", "type": "text", "primaryKey": false, "notNull": false}, "preferred_language": {"name": "preferred_language", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"individual_party_id_fkey": {"name": "individual_party_id_fkey", "tableFrom": "individual", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.industry": {"name": "industry", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"industry_name_key": {"name": "industry_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.inspection_photos": {"name": "inspection_photos", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "inspection_id": {"name": "inspection_id", "type": "integer", "primaryKey": false, "notNull": true}, "photo_type": {"name": "photo_type", "type": "photo_type_enhanced", "typeSchema": "public", "primaryKey": false, "notNull": true}, "file_url": {"name": "file_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "captured_at": {"name": "captured_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"inspection_photos_inspection_id_idx": {"name": "inspection_photos_inspection_id_idx", "columns": [{"expression": "inspection_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"inspection_photos_inspection_id_vehicle_inspections_immutable_id_fk": {"name": "inspection_photos_inspection_id_vehicle_inspections_immutable_id_fk", "tableFrom": "inspection_photos", "tableTo": "vehicle_inspections_immutable", "columnsFrom": ["inspection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.issuing_authority": {"name": "issuing_authority", "schema": "", "columns": {"party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"issuing_authority_party_id_fkey": {"name": "issuing_authority_party_id_fkey", "tableFrom": "issuing_authority", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"issuing_authority_name_key": {"name": "issuing_authority_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.lead": {"name": "lead", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "company": {"name": "company", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "status_id": {"name": "status_id", "type": "integer", "primaryKey": false, "notNull": true}, "source_id": {"name": "source_id", "type": "integer", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "text", "primaryKey": false, "notNull": false}, "annual_revenue": {"name": "annual_revenue", "type": "integer", "primaryKey": false, "notNull": false}, "number_of_employees": {"name": "number_of_employees", "type": "integer", "primaryKey": false, "notNull": false}, "industry": {"name": "industry", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_converted": {"name": "is_converted", "type": "boolean", "primaryKey": false, "notNull": true}, "converted_date": {"name": "converted_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "converted_account_id": {"name": "converted_account_id", "type": "integer", "primaryKey": false, "notNull": false}, "converted_contact_id": {"name": "converted_contact_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"lead_party_id_fkey": {"name": "lead_party_id_fkey", "tableFrom": "lead", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "lead_source_id_fkey": {"name": "lead_source_id_fkey", "tableFrom": "lead", "tableTo": "lead_source", "columnsFrom": ["source_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "lead_status_id_fkey": {"name": "lead_status_id_fkey", "tableFrom": "lead", "tableTo": "lead_status", "columnsFrom": ["status_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.lead_source": {"name": "lead_source", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"lead_source_name_key": {"name": "lead_source_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.lead_status": {"name": "lead_status", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"lead_status_name_key": {"name": "lead_status_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.listing_history": {"name": "listing_history", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "listing_id": {"name": "listing_id", "type": "integer", "primaryKey": false, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "effective_from": {"name": "effective_from", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "effective_to": {"name": "effective_to", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "fraction": {"name": "fraction", "type": "double precision", "primaryKey": false, "notNull": true}, "asking_price": {"name": "asking_price", "type": "double precision", "primaryKey": false, "notNull": true}, "condition": {"name": "condition", "type": "conditionenum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "mileage": {"name": "mileage", "type": "double precision", "primaryKey": false, "notNull": false}, "listing_type": {"name": "listing_type", "type": "listingtypeenum", "primaryKey": false, "notNull": true}, "audience": {"name": "audience", "type": "audienceenum", "primaryKey": false, "notNull": true}, "change_type": {"name": "change_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "changed_by": {"name": "changed_by", "type": "integer", "primaryKey": false, "notNull": true}, "change_reason": {"name": "change_reason", "type": "text", "primaryKey": false, "notNull": false}, "previous_values": {"name": "previous_values", "type": "jsonb", "primaryKey": false, "notNull": false}, "changed_at": {"name": "changed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"listing_history_listing_id_idx": {"name": "listing_history_listing_id_idx", "columns": [{"expression": "listing_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "listing_history_changed_at_idx": {"name": "listing_history_changed_at_idx", "columns": [{"expression": "changed_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"listing_history_listing_id_fkey": {"name": "listing_history_listing_id_fkey", "tableFrom": "listing_history", "tableTo": "listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "listing_history_changed_by_fkey": {"name": "listing_history_changed_by_fkey", "tableFrom": "listing_history", "tableTo": "party", "columnsFrom": ["changed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.listing_interest_expressions": {"name": "listing_interest_expressions", "schema": "", "columns": {"listing_id": {"name": "listing_id", "type": "integer", "primaryKey": false, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"listing_interest_expressions_listing_id_fkey": {"name": "listing_interest_expressions_listing_id_fkey", "tableFrom": "listing_interest_expressions", "tableTo": "listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "listing_interest_expressions_party_id_fkey": {"name": "listing_interest_expressions_party_id_fkey", "tableFrom": "listing_interest_expressions", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.listing_media": {"name": "listing_media", "schema": "", "columns": {"listing_id": {"name": "listing_id", "type": "integer", "primaryKey": false, "notNull": true}, "media_path": {"name": "media_path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"listing_media_listing_id_fkey": {"name": "listing_media_listing_id_fkey", "tableFrom": "listing_media", "tableTo": "listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.listing_offers": {"name": "listing_offers", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "listing_id": {"name": "listing_id", "type": "integer", "primaryKey": false, "notNull": true}, "offer_type": {"name": "offer_type", "type": "offer_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "fraction": {"name": "fraction", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "duration_type": {"name": "duration_type", "type": "duration_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "finance_method": {"name": "finance_method", "type": "finance_method", "typeSchema": "public", "primaryKey": false, "notNull": true}, "financing_terms": {"name": "financing_terms", "type": "jsonb", "primaryKey": false, "notNull": false}, "terms": {"name": "terms", "type": "jsonb", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "offer_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "valid_from": {"name": "valid_from", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "valid_to": {"name": "valid_to", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "'infinity'::timestamp"}, "contract_terms": {"name": "contract_terms", "type": "jsonb", "primaryKey": false, "notNull": true}, "payment_terms": {"name": "payment_terms", "type": "jsonb", "primaryKey": false, "notNull": true}, "usage_terms": {"name": "usage_terms", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"listing_offers_listing_id_listings_id_fk": {"name": "listing_offers_listing_id_listings_id_fk", "tableFrom": "listing_offers", "tableTo": "listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.listings": {"name": "listings", "schema": "", "columns": {"party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "effective_from": {"name": "effective_from", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "effective_to": {"name": "effective_to", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "'infinity'::timestamp"}, "fraction": {"name": "fraction", "type": "double precision", "primaryKey": false, "notNull": true}, "asking_price": {"name": "asking_price", "type": "double precision", "primaryKey": false, "notNull": true}, "condition": {"name": "condition", "type": "conditionenum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "mileage": {"name": "mileage", "type": "double precision", "primaryKey": false, "notNull": false}, "listing_type": {"name": "listing_type", "type": "listingtypeenum", "primaryKey": false, "notNull": true}, "audience": {"name": "audience", "type": "audienceenum", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"listings_party_id_fkey": {"name": "listings_party_id_fkey", "tableFrom": "listings", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "listings_vehicle_id_fkey": {"name": "listings_vehicle_id_fkey", "tableFrom": "listings", "tableTo": "vehicles", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.match_rule_type": {"name": "match_rule_type", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "confidence": {"name": "confidence", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notification_logs": {"name": "notification_logs", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "subscription_id": {"name": "subscription_id", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "payload": {"name": "payload", "type": "json", "primaryKey": false, "notNull": true}, "sent_at": {"name": "sent_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "delivery_status": {"name": "delivery_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "clicked_at": {"name": "clicked_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"notification_logs_subscription_id_push_subscriptions_id_fk": {"name": "notification_logs_subscription_id_push_subscriptions_id_fk", "tableFrom": "notification_logs", "tableTo": "push_subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.opportunities": {"name": "opportunities", "schema": "", "columns": {"opportunity_name": {"name": "opportunity_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "double precision", "primaryKey": false, "notNull": true}, "fraction": {"name": "fraction", "type": "double precision", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"opportunities_company_id_fkey": {"name": "opportunities_company_id_fkey", "tableFrom": "opportunities", "tableTo": "company", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.opportunity_stage": {"name": "opportunity_stage", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "probability": {"name": "probability", "type": "integer", "primaryKey": false, "notNull": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"opportunity_stage_name_key": {"name": "opportunity_stage_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.opportunity_type": {"name": "opportunity_type", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"opportunity_type_name_key": {"name": "opportunity_type_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization": {"name": "organization", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "industry": {"name": "industry", "type": "text", "primaryKey": false, "notNull": false}, "number_of_employees": {"name": "number_of_employees", "type": "integer", "primaryKey": false, "notNull": false}, "annual_revenue": {"name": "annual_revenue", "type": "integer", "primaryKey": false, "notNull": false}, "website_url": {"name": "website_url", "type": "text", "primaryKey": false, "notNull": false}, "logo_url": {"name": "logo_url", "type": "text", "primaryKey": false, "notNull": false}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"organization_party_id_fkey": {"name": "organization_party_id_fkey", "tableFrom": "organization", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.party": {"name": "party", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_type_id": {"name": "party_type_id", "type": "integer", "primaryKey": false, "notNull": true}, "external_id": {"name": "external_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"party_party_type_id_fkey": {"name": "party_party_type_id_fkey", "tableFrom": "party", "tableTo": "party_type", "columnsFrom": ["party_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.party_identification": {"name": "party_identification", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "identification_type_id": {"name": "identification_type_id", "type": "integer", "primaryKey": false, "notNull": true}, "document_number": {"name": "document_number", "type": "text", "primaryKey": false, "notNull": true}, "issuing_authority": {"name": "issuing_authority", "type": "text", "primaryKey": false, "notNull": false}, "issue_date": {"name": "issue_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "expiry_date": {"name": "expiry_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "verification_date": {"name": "verification_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "verification_method": {"name": "verification_method", "type": "text", "primaryKey": false, "notNull": false}, "document_image_url": {"name": "document_image_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"party_identification_identification_type_id_fkey": {"name": "party_identification_identification_type_id_fkey", "tableFrom": "party_identification", "tableTo": "identification_type", "columnsFrom": ["identification_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "party_identification_party_id_fkey": {"name": "party_identification_party_id_fkey", "tableFrom": "party_identification", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.party_status": {"name": "party_status", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "party_status_enum", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"party_status_party_id_party_id_fk": {"name": "party_status_party_id_party_id_fk", "tableFrom": "party_status", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "party_status_party_id_fkey": {"name": "party_status_party_id_fkey", "tableFrom": "party_status", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.party_type": {"name": "party_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"party_type_name_key": {"name": "party_type_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.product_type": {"name": "product_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"product_type_name_key": {"name": "product_type_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.province_state": {"name": "province_state", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "countryId": {"name": "countryId", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"province_state_countryId_countries_id_fk": {"name": "province_state_countryId_countries_id_fk", "tableFrom": "province_state", "tableTo": "countries", "columnsFrom": ["countryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"uq_province_or_state_name_country": {"name": "uq_province_or_state_name_country", "nullsNotDistinct": false, "columns": ["name", "countryId"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.push_subscriptions": {"name": "push_subscriptions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": false}, "endpoint": {"name": "endpoint", "type": "text", "primaryKey": false, "notNull": true}, "p256dh_key": {"name": "p256dh_key", "type": "text", "primaryKey": false, "notNull": true}, "auth_key": {"name": "auth_key", "type": "text", "primaryKey": false, "notNull": true}, "expiration_time": {"name": "expiration_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "device_info": {"name": "device_info", "type": "json", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.record_type": {"name": "record_type", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"record_type_name_key": {"name": "record_type_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.relationship_type": {"name": "relationship_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"relationship_type_name_key": {"name": "relationship_type_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.requirement_dependency": {"name": "requirement_dependency", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "requirementId": {"name": "requirementId", "type": "integer", "primaryKey": false, "notNull": true}, "dependsOnId": {"name": "dependsOnId", "type": "integer", "primaryKey": false, "notNull": true}, "condition": {"name": "condition", "type": "jsonb", "primaryKey": false, "notNull": false}, "isRequired": {"name": "isRequired", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"requirement_dependency_requirementId_compliance_requirement_id_fk": {"name": "requirement_dependency_requirementId_compliance_requirement_id_fk", "tableFrom": "requirement_dependency", "tableTo": "compliance_requirement", "columnsFrom": ["requirementId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "requirement_dependency_dependsOnId_compliance_requirement_id_fk": {"name": "requirement_dependency_dependsOnId_compliance_requirement_id_fk", "tableFrom": "requirement_dependency", "tableTo": "compliance_requirement", "columnsFrom": ["dependsOnId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.service_providers": {"name": "service_providers", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "service_type": {"name": "service_type", "type": "servicetypeenum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "effective_from": {"name": "effective_from", "type": "date", "primaryKey": false, "notNull": true}, "effective_to": {"name": "effective_to", "type": "date", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.social_media_type": {"name": "social_media_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "base_url": {"name": "base_url", "type": "text", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"social_media_type_name_key": {"name": "social_media_type_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.social_profile": {"name": "social_profile", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "social_media_type_id": {"name": "social_media_type_id", "type": "integer", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": true}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"social_profile_party_id_fkey": {"name": "social_profile_party_id_fkey", "tableFrom": "social_profile", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "social_profile_social_media_type_id_fkey": {"name": "social_profile_social_media_type_id_fkey", "tableFrom": "social_profile", "tableTo": "social_media_type", "columnsFrom": ["social_media_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.status_change_notification": {"name": "status_change_notification", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "statusHistoryId": {"name": "statusHistoryId", "type": "integer", "primaryKey": false, "notNull": true}, "notificationType": {"name": "notificationType", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "recipientId": {"name": "recipientId", "type": "integer", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": false}, "sent_at": {"name": "sent_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"status_change_notification_statusHistoryId_status_history_id_fk": {"name": "status_change_notification_statusHistoryId_status_history_id_fk", "tableFrom": "status_change_notification", "tableTo": "status_history", "columnsFrom": ["statusHistoryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "status_change_notification_recipientId_party_id_fk": {"name": "status_change_notification_recipientId_party_id_fk", "tableFrom": "status_change_notification", "tableTo": "party", "columnsFrom": ["recipientId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.status_history": {"name": "status_history", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "entity_type": {"name": "entity_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "integer", "primaryKey": false, "notNull": true}, "from_status": {"name": "from_status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "to_status": {"name": "to_status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "transition_rule_id": {"name": "transition_rule_id", "type": "integer", "primaryKey": false, "notNull": false}, "changed_by": {"name": "changed_by", "type": "integer", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "effective_from": {"name": "effective_from", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "effective_to": {"name": "effective_to", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"status_history_entity_idx": {"name": "status_history_entity_idx", "columns": [{"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "current_status_history_idx": {"name": "current_status_history_idx", "columns": [{"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "effective_to", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"status_history_transition_rule_id_status_transition_rules_id_fk": {"name": "status_history_transition_rule_id_status_transition_rules_id_fk", "tableFrom": "status_history", "tableTo": "status_transition_rules", "columnsFrom": ["transition_rule_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "status_history_changed_by_party_id_fk": {"name": "status_history_changed_by_party_id_fk", "tableFrom": "status_history", "tableTo": "party", "columnsFrom": ["changed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.status_sla": {"name": "status_sla", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "entityType": {"name": "entityType", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "expectedDurationHours": {"name": "expectedDurationHours", "type": "integer", "primaryKey": false, "notNull": true}, "escalationRules": {"name": "escalationRules", "type": "jsonb", "primaryKey": false, "notNull": false}, "notificationRules": {"name": "notificationRules", "type": "jsonb", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.status_sla_tracking": {"name": "status_sla_tracking", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "statusHistoryId": {"name": "statusHistoryId", "type": "integer", "primaryKey": false, "notNull": true}, "slaId": {"name": "slaId", "type": "integer", "primaryKey": false, "notNull": true}, "due_at": {"name": "due_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "completed_at": {"name": "completed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "isBreached": {"name": "isBreached", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "escalationLevel": {"name": "escalationLevel", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"status_sla_tracking_statusHistoryId_status_history_id_fk": {"name": "status_sla_tracking_statusHistoryId_status_history_id_fk", "tableFrom": "status_sla_tracking", "tableTo": "status_history", "columnsFrom": ["statusHistoryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "status_sla_tracking_slaId_status_sla_id_fk": {"name": "status_sla_tracking_slaId_status_sla_id_fk", "tableFrom": "status_sla_tracking", "tableTo": "status_sla", "columnsFrom": ["slaId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.status_transition_rules": {"name": "status_transition_rules", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "entityType": {"name": "entityType", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "fromStatus": {"name": "fromStatus", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "toStatus": {"name": "to<PERSON><PERSON><PERSON>", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "allowedRoles": {"name": "allowedRoles", "type": "jsonb", "primaryKey": false, "notNull": true}, "requiresApproval": {"name": "requiresApproval", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "validationRules": {"name": "validationRules", "type": "jsonb", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"status_transition_idx": {"name": "status_transition_idx", "columns": [{"expression": "entityType", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "fromStatus", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "to<PERSON><PERSON><PERSON>", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription_status": {"name": "subscription_status", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"subscription_status_name_key": {"name": "subscription_status_name_key", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "family_name": {"name": "family_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "given_name": {"name": "given_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "org_id": {"name": "org_id", "type": "<PERSON><PERSON><PERSON>(16)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_key": {"name": "users_username_key", "nullsNotDistinct": false, "columns": ["username"]}, "users_email_key": {"name": "users_email_key", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_access_events": {"name": "vehicle_access_events", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "permission_type": {"name": "permission_type", "type": "permission_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "effective_from": {"name": "effective_from", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "effective_to": {"name": "effective_to", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "granted_by": {"name": "granted_by", "type": "integer", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "event_timestamp": {"name": "event_timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"vehicle_access_events_vehicle_id_idx": {"name": "vehicle_access_events_vehicle_id_idx", "columns": [{"expression": "vehicle_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_access_events_party_id_idx": {"name": "vehicle_access_events_party_id_idx", "columns": [{"expression": "party_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_access_events_effective_idx": {"name": "vehicle_access_events_effective_idx", "columns": [{"expression": "effective_from", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "effective_to", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_access_events_timestamp_idx": {"name": "vehicle_access_events_timestamp_idx", "columns": [{"expression": "event_timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_access_events_vehicle_id_vehicles_id_fk": {"name": "vehicle_access_events_vehicle_id_vehicles_id_fk", "tableFrom": "vehicle_access_events", "tableTo": "vehicles", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vehicle_access_events_party_id_party_id_fk": {"name": "vehicle_access_events_party_id_party_id_fk", "tableFrom": "vehicle_access_events", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vehicle_access_events_granted_by_party_id_fk": {"name": "vehicle_access_events_granted_by_party_id_fk", "tableFrom": "vehicle_access_events", "tableTo": "party", "columnsFrom": ["granted_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_documents": {"name": "vehicle_documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "media_path": {"name": "media_path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "document_type": {"name": "document_type", "type": "documenttype", "typeSchema": "public", "primaryKey": false, "notNull": true}, "expiration_date": {"name": "expiration_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_documents_id": {"name": "ix_vehicle_documents_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_documents_vehicle_id_fkey": {"name": "vehicle_documents_vehicle_id_fkey", "tableFrom": "vehicle_documents", "tableTo": "vehicles", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_handovers": {"name": "vehicle_handovers", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "handover_type": {"name": "handover_type", "type": "handover_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "booking_reference": {"name": "booking_reference", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "from_party_id": {"name": "from_party_id", "type": "integer", "primaryKey": false, "notNull": true}, "to_party_id": {"name": "to_party_id", "type": "integer", "primaryKey": false, "notNull": true}, "scheduled_time": {"name": "scheduled_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "handover_location": {"name": "handover_location", "type": "text", "primaryKey": false, "notNull": false}, "handover_coordinates": {"name": "handover_coordinates", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"vehicle_handovers_vehicle_id_idx": {"name": "vehicle_handovers_vehicle_id_idx", "columns": [{"expression": "vehicle_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_handovers_booking_reference_idx": {"name": "vehicle_handovers_booking_reference_idx", "columns": [{"expression": "booking_reference", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_handovers_scheduled_time_idx": {"name": "vehicle_handovers_scheduled_time_idx", "columns": [{"expression": "scheduled_time", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_handovers_vehicle_id_vehicles_id_fk": {"name": "vehicle_handovers_vehicle_id_vehicles_id_fk", "tableFrom": "vehicle_handovers", "tableTo": "vehicles", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vehicle_handovers_from_party_id_party_id_fk": {"name": "vehicle_handovers_from_party_id_party_id_fk", "tableFrom": "vehicle_handovers", "tableTo": "party", "columnsFrom": ["from_party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vehicle_handovers_to_party_id_party_id_fk": {"name": "vehicle_handovers_to_party_id_party_id_fk", "tableFrom": "vehicle_handovers", "tableTo": "party", "columnsFrom": ["to_party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vehicle_handovers_created_by_party_id_fk": {"name": "vehicle_handovers_created_by_party_id_fk", "tableFrom": "vehicle_handovers", "tableTo": "party", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_inspections": {"name": "vehicle_inspections", "schema": "", "columns": {"scratches": {"name": "scratches", "type": "conditionlevel", "primaryKey": false, "notNull": true}, "dents": {"name": "dents", "type": "conditionlevel", "primaryKey": false, "notNull": true}, "tires": {"name": "tires", "type": "generalcondition", "typeSchema": "public", "primaryKey": false, "notNull": true}, "lights": {"name": "lights", "type": "lightscondition", "typeSchema": "public", "primaryKey": false, "notNull": true}, "cleanliness": {"name": "cleanliness", "type": "cleanlinesslevel", "typeSchema": "public", "primaryKey": false, "notNull": true}, "seats": {"name": "seats", "type": "generalcondition", "typeSchema": "public", "primaryKey": false, "notNull": true}, "dashboard_controls": {"name": "dashboard_controls", "type": "dashboardcondition", "typeSchema": "public", "primaryKey": false, "notNull": true}, "odors": {"name": "odors", "type": "odorlevel", "typeSchema": "public", "primaryKey": false, "notNull": true}, "odometer": {"name": "odometer", "type": "integer", "primaryKey": false, "notNull": true}, "known_issues": {"name": "known_issues", "type": "text", "primaryKey": false, "notNull": false}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "possession_id": {"name": "possession_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_inspections_id": {"name": "ix_vehicle_inspections_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_inspections_possession_id_fkey": {"name": "vehicle_inspections_possession_id_fkey", "tableFrom": "vehicle_inspections", "tableTo": "vehicle_possessions", "columnsFrom": ["possession_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "vehicle_inspections_vehicle_id_fkey": {"name": "vehicle_inspections_vehicle_id_fkey", "tableFrom": "vehicle_inspections", "tableTo": "vehicles", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_inspections_immutable": {"name": "vehicle_inspections_immutable", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "handover_id": {"name": "handover_id", "type": "integer", "primaryKey": false, "notNull": false}, "inspector_party_id": {"name": "inspector_party_id", "type": "integer", "primaryKey": false, "notNull": true}, "inspection_type": {"name": "inspection_type", "type": "inspection_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "odometer": {"name": "odometer", "type": "integer", "primaryKey": false, "notNull": true}, "fuel_level": {"name": "fuel_level", "type": "integer", "primaryKey": false, "notNull": false}, "scratches": {"name": "scratches", "type": "conditionlevel", "primaryKey": false, "notNull": false}, "dents": {"name": "dents", "type": "conditionlevel", "primaryKey": false, "notNull": false}, "tires": {"name": "tires", "type": "generalcondition", "typeSchema": "public", "primaryKey": false, "notNull": false}, "lights": {"name": "lights", "type": "lightscondition", "typeSchema": "public", "primaryKey": false, "notNull": false}, "cleanliness": {"name": "cleanliness", "type": "cleanlinesslevel", "typeSchema": "public", "primaryKey": false, "notNull": false}, "seats": {"name": "seats", "type": "generalcondition", "typeSchema": "public", "primaryKey": false, "notNull": false}, "dashboard_controls": {"name": "dashboard_controls", "type": "dashboardcondition", "typeSchema": "public", "primaryKey": false, "notNull": false}, "odors": {"name": "odors", "type": "odorlevel", "typeSchema": "public", "primaryKey": false, "notNull": false}, "overall_condition": {"name": "overall_condition", "type": "condition_enhanced", "typeSchema": "public", "primaryKey": false, "notNull": true}, "known_issues": {"name": "known_issues", "type": "text", "primaryKey": false, "notNull": false}, "new_damage": {"name": "new_damage", "type": "text", "primaryKey": false, "notNull": false}, "items_in_vehicle": {"name": "items_in_vehicle", "type": "text", "primaryKey": false, "notNull": false}, "inspector_signature": {"name": "inspector_signature", "type": "text", "primaryKey": false, "notNull": false}, "inspection_completed_at": {"name": "inspection_completed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "related_inspection_id": {"name": "related_inspection_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"vehicle_inspections_immutable_vehicle_id_idx": {"name": "vehicle_inspections_immutable_vehicle_id_idx", "columns": [{"expression": "vehicle_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_inspections_immutable_handover_id_idx": {"name": "vehicle_inspections_immutable_handover_id_idx", "columns": [{"expression": "handover_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_inspections_immutable_inspector_idx": {"name": "vehicle_inspections_immutable_inspector_idx", "columns": [{"expression": "inspector_party_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_inspections_immutable_completed_at_idx": {"name": "vehicle_inspections_immutable_completed_at_idx", "columns": [{"expression": "inspection_completed_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_inspections_immutable_vehicle_id_vehicles_id_fk": {"name": "vehicle_inspections_immutable_vehicle_id_vehicles_id_fk", "tableFrom": "vehicle_inspections_immutable", "tableTo": "vehicles", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vehicle_inspections_immutable_handover_id_vehicle_handovers_id_fk": {"name": "vehicle_inspections_immutable_handover_id_vehicle_handovers_id_fk", "tableFrom": "vehicle_inspections_immutable", "tableTo": "vehicle_handovers", "columnsFrom": ["handover_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vehicle_inspections_immutable_inspector_party_id_party_id_fk": {"name": "vehicle_inspections_immutable_inspector_party_id_party_id_fk", "tableFrom": "vehicle_inspections_immutable", "tableTo": "party", "columnsFrom": ["inspector_party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vehicle_inspections_immutable_related_inspection_fkey": {"name": "vehicle_inspections_immutable_related_inspection_fkey", "tableFrom": "vehicle_inspections_immutable", "tableTo": "vehicle_inspections_immutable", "columnsFrom": ["related_inspection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"fuel_level_range": {"name": "fuel_level_range", "value": "fuel_level >= 0 AND fuel_level <= 100"}}, "isRLSEnabled": false}, "public.vehicle_maintenance": {"name": "vehicle_maintenance", "schema": "", "columns": {"vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "due_odometer": {"name": "due_odometer", "type": "double precision", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "vehicleservicestatus", "typeSchema": "public", "primaryKey": false, "notNull": true}, "expected_cost": {"name": "expected_cost", "type": "double precision", "primaryKey": false, "notNull": true}, "completed_date": {"name": "completed_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_odometer": {"name": "completed_odometer", "type": "double precision", "primaryKey": false, "notNull": false}, "actual_cost": {"name": "actual_cost", "type": "double precision", "primaryKey": false, "notNull": false}, "technician_notes": {"name": "technician_notes", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "service_provider": {"name": "service_provider", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_scheduled": {"name": "is_scheduled", "type": "boolean", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}}, "indexes": {"ix_vehicle_maintenance_id": {"name": "ix_vehicle_maintenance_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_maintenance_vehicle_id_fkey": {"name": "vehicle_maintenance_vehicle_id_fkey", "tableFrom": "vehicle_maintenance", "tableTo": "vehicles", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_make": {"name": "vehicle_make", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_make_id": {"name": "ix_vehicle_make_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_media": {"name": "vehicle_media", "schema": "", "columns": {"vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "media_path": {"name": "media_path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_media_id": {"name": "ix_vehicle_media_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_media_vehicle_id_fkey": {"name": "vehicle_media_vehicle_id_fkey", "tableFrom": "vehicle_media", "tableTo": "vehicles", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_model": {"name": "vehicle_model", "schema": "", "columns": {"make_id": {"name": "make_id", "type": "integer", "primaryKey": false, "notNull": true}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": false}, "first_year": {"name": "first_year", "type": "integer", "primaryKey": false, "notNull": false}, "last_year": {"name": "last_year", "type": "integer", "primaryKey": false, "notNull": false}, "body_type": {"name": "body_type", "type": "body_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_model_id": {"name": "ix_vehicle_model_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_model_make_id_fkey": {"name": "vehicle_model_make_id_fkey", "tableFrom": "vehicle_model", "tableTo": "vehicle_make", "columnsFrom": ["make_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_model_media": {"name": "vehicle_model_media", "schema": "", "columns": {"vehicle_model_id": {"name": "vehicle_model_id", "type": "integer", "primaryKey": false, "notNull": true}, "media_path": {"name": "media_path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_model_media_id": {"name": "ix_vehicle_model_media_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_model_media_vehicle_model_id_fkey": {"name": "vehicle_model_media_vehicle_model_id_fkey", "tableFrom": "vehicle_model_media", "tableTo": "vehicle_model", "columnsFrom": ["vehicle_model_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_photos": {"name": "vehicle_photos", "schema": "", "columns": {"inspection_id": {"name": "inspection_id", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "phototype", "typeSchema": "public", "primaryKey": false, "notNull": true}, "file_url": {"name": "file_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_photos_id": {"name": "ix_vehicle_photos_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_photos_inspection_id_fkey": {"name": "vehicle_photos_inspection_id_fkey", "tableFrom": "vehicle_photos", "tableTo": "vehicle_inspections", "columnsFrom": ["inspection_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_possession_events": {"name": "vehicle_possession_events", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "possessor_party_id": {"name": "possessor_party_id", "type": "integer", "primaryKey": false, "notNull": true}, "possession_start": {"name": "possession_start", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "possession_end": {"name": "possession_end", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "possession_type": {"name": "possession_type", "type": "possession_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "trigger_type": {"name": "trigger_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "trigger_reference": {"name": "trigger_reference", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "handover_id": {"name": "handover_id", "type": "integer", "primaryKey": false, "notNull": false}, "recorded_by": {"name": "recorded_by", "type": "integer", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "event_timestamp": {"name": "event_timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"vehicle_possession_events_vehicle_id_idx": {"name": "vehicle_possession_events_vehicle_id_idx", "columns": [{"expression": "vehicle_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_possession_events_possessor_idx": {"name": "vehicle_possession_events_possessor_idx", "columns": [{"expression": "possessor_party_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_possession_events_dates_idx": {"name": "vehicle_possession_events_dates_idx", "columns": [{"expression": "possession_start", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "possession_end", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_possession_events_timestamp_idx": {"name": "vehicle_possession_events_timestamp_idx", "columns": [{"expression": "event_timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_possession_events_vehicle_id_vehicles_id_fk": {"name": "vehicle_possession_events_vehicle_id_vehicles_id_fk", "tableFrom": "vehicle_possession_events", "tableTo": "vehicles", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vehicle_possession_events_possessor_party_id_party_id_fk": {"name": "vehicle_possession_events_possessor_party_id_party_id_fk", "tableFrom": "vehicle_possession_events", "tableTo": "party", "columnsFrom": ["possessor_party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vehicle_possession_events_handover_id_vehicle_handovers_id_fk": {"name": "vehicle_possession_events_handover_id_vehicle_handovers_id_fk", "tableFrom": "vehicle_possession_events", "tableTo": "vehicle_handovers", "columnsFrom": ["handover_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vehicle_possession_events_recorded_by_party_id_fk": {"name": "vehicle_possession_events_recorded_by_party_id_fk", "tableFrom": "vehicle_possession_events", "tableTo": "party", "columnsFrom": ["recorded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_possessions": {"name": "vehicle_possessions", "schema": "", "columns": {"from_party_id": {"name": "from_party_id", "type": "integer", "primaryKey": false, "notNull": true}, "to_party_id": {"name": "to_party_id", "type": "integer", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "handover_expected_datetime": {"name": "handover_expected_datetime", "type": "timestamp", "primaryKey": false, "notNull": false}, "handover_actual_datetime": {"name": "handover_actual_datetime", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "possessionstatus", "typeSchema": "public", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_possessions_id": {"name": "ix_vehicle_possessions_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_possessions_from_party_id_fkey": {"name": "vehicle_possessions_from_party_id_fkey", "tableFrom": "vehicle_possessions", "tableTo": "party", "columnsFrom": ["from_party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vehicle_possessions_to_party_id_fkey": {"name": "vehicle_possessions_to_party_id_fkey", "tableFrom": "vehicle_possessions", "tableTo": "party", "columnsFrom": ["to_party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vehicle_possessions_vehicle_id_fkey": {"name": "vehicle_possessions_vehicle_id_fkey", "tableFrom": "vehicle_possessions", "tableTo": "vehicles", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_variant": {"name": "vehicle_variant", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "model_id": {"name": "model_id", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "trim_name": {"name": "trim_name", "type": "text", "primaryKey": false, "notNull": false}, "year": {"name": "year", "type": "integer", "primaryKey": false, "notNull": true}, "engine": {"name": "engine", "type": "text", "primaryKey": false, "notNull": false}, "drivetrain": {"name": "drivetrain", "type": "drivetrain", "typeSchema": "public", "primaryKey": false, "notNull": false}, "body_type": {"name": "body_type", "type": "body_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "seats": {"name": "seats", "type": "integer", "primaryKey": false, "notNull": false}, "doors": {"name": "doors", "type": "integer", "primaryKey": false, "notNull": false}, "msrp": {"name": "msrp", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "features": {"name": "features", "type": "jsonb", "primaryKey": false, "notNull": false}, "specs": {"name": "specs", "type": "jsonb", "primaryKey": false, "notNull": false}, "fuel_type": {"name": "fuel_type", "type": "fuel_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "transmission": {"name": "transmission", "type": "transmission", "typeSchema": "public", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_variant_id": {"name": "ix_vehicle_variant_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ix_vehicle_variant_name_key": {"name": "ix_vehicle_variant_name_key", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_variant_model_id_fkey": {"name": "vehicle_variant_model_id_fkey", "tableFrom": "vehicle_variant", "tableTo": "vehicle_model", "columnsFrom": ["model_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicles": {"name": "vehicles", "schema": "", "columns": {"party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "model_id": {"name": "model_id", "type": "integer", "primaryKey": false, "notNull": true}, "vin_number": {"name": "vin_number", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "vehicle_registration": {"name": "vehicle_registration", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "country_id": {"name": "country_id", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "manufacturing_year": {"name": "manufacturing_year", "type": "integer", "primaryKey": false, "notNull": false}, "purchase_date": {"name": "purchase_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicles_id": {"name": "ix_vehicles_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicles_country_id_countries_id_fk": {"name": "vehicles_country_id_countries_id_fk", "tableFrom": "vehicles", "tableTo": "countries", "columnsFrom": ["country_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vehicles_model_id_fkey": {"name": "vehicles_model_id_fkey", "tableFrom": "vehicles", "tableTo": "vehicle_model", "columnsFrom": ["model_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "vehicles_party_id_fkey": {"name": "vehicles_party_id_fkey", "tableFrom": "vehicles", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"vehicles_vin_number_key": {"name": "vehicles_vin_number_key", "nullsNotDistinct": false, "columns": ["vin_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"verifying_party_id": {"name": "verifying_party_id", "type": "integer", "primaryKey": false, "notNull": true}, "verification_outcome": {"name": "verification_outcome", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "cost": {"name": "cost", "type": "double precision", "primaryKey": false, "notNull": false}, "verification_type": {"name": "verification_type", "type": "verificationtypeenum", "typeSchema": "public", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"verification_verifying_party_id_fkey": {"name": "verification_verifying_party_id_fkey", "tableFrom": "verification", "tableTo": "party", "columnsFrom": ["verifying_party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.voting_threshold": {"name": "voting_threshold", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": false, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "unanimous": {"name": "unanimous", "type": "boolean", "primaryKey": false, "notNull": false}, "simple_majority": {"name": "simple_majority", "type": "boolean", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_voting_threshold_id": {"name": "ix_voting_threshold_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "int4_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {"voting_threshold_pkey": {"name": "voting_threshold_pkey", "columns": ["id", "company_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.h_application_decisions": {"name": "h_application_decisions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "decision": {"name": "decision", "type": "application_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "application_id": {"name": "application_id", "type": "integer", "primaryKey": false, "notNull": true}, "reviewer_id": {"name": "reviewer_id", "type": "integer", "primaryKey": false, "notNull": true}, "decision_at": {"name": "decision_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"h_application_decisions_application_id_h_applications_id_fk": {"name": "h_application_decisions_application_id_h_applications_id_fk", "tableFrom": "h_application_decisions", "tableTo": "h_applications", "columnsFrom": ["application_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "h_application_decisions_reviewer_id_users_id_fk": {"name": "h_application_decisions_reviewer_id_users_id_fk", "tableFrom": "h_application_decisions", "tableTo": "users", "columnsFrom": ["reviewer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.h_application_documents": {"name": "h_application_documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "application_id": {"name": "application_id", "type": "integer", "primaryKey": false, "notNull": true}, "documentType": {"name": "documentType", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "documentUrl": {"name": "documentUrl", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "uploaded_at": {"name": "uploaded_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"h_application_documents_application_id_h_applications_id_fk": {"name": "h_application_documents_application_id_h_applications_id_fk", "tableFrom": "h_application_documents", "tableTo": "h_applications", "columnsFrom": ["application_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.h_application_documents_status": {"name": "h_application_documents_status", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "application_document_id": {"name": "application_document_id", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "document_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status_at": {"name": "status_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "status_by": {"name": "status_by", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"h_application_documents_status_application_document_id_h_application_documents_id_fk": {"name": "h_application_documents_status_application_document_id_h_application_documents_id_fk", "tableFrom": "h_application_documents_status", "tableTo": "h_application_documents", "columnsFrom": ["application_document_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "h_application_documents_status_status_by_party_id_fk": {"name": "h_application_documents_status_status_by_party_id_fk", "tableFrom": "h_application_documents_status", "tableTo": "party", "columnsFrom": ["status_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.h_applications": {"name": "h_applications", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "applicant_id": {"name": "applicant_id", "type": "integer", "primaryKey": false, "notNull": true}, "listing_id": {"name": "listing_id", "type": "integer", "primaryKey": false, "notNull": true}, "application_details": {"name": "application_details", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"h_applications_applicant_id_party_id_fk": {"name": "h_applications_applicant_id_party_id_fk", "tableFrom": "h_applications", "tableTo": "party", "columnsFrom": ["applicant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "h_applications_listing_id_h_listings_id_fk": {"name": "h_applications_listing_id_h_listings_id_fk", "tableFrom": "h_applications", "tableTo": "h_listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.h_listing_approval_status": {"name": "h_listing_approval_status", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "listing_id": {"name": "listing_id", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "listing_approval_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "status_at": {"name": "status_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "status_by": {"name": "status_by", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"h_listing_approval_status_listing_id_h_listings_id_fk": {"name": "h_listing_approval_status_listing_id_h_listings_id_fk", "tableFrom": "h_listing_approval_status", "tableTo": "h_listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "h_listing_approval_status_status_by_party_id_fk": {"name": "h_listing_approval_status_status_by_party_id_fk", "tableFrom": "h_listing_approval_status", "tableTo": "party", "columnsFrom": ["status_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.h_listing_publish_status": {"name": "h_listing_publish_status", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "listing_id": {"name": "listing_id", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "listing_publish_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status_at": {"name": "status_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "status_by": {"name": "status_by", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"h_listing_publish_status_listing_id_h_listings_id_fk": {"name": "h_listing_publish_status_listing_id_h_listings_id_fk", "tableFrom": "h_listing_publish_status", "tableTo": "h_listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "h_listing_publish_status_status_by_party_id_fk": {"name": "h_listing_publish_status_status_by_party_id_fk", "tableFrom": "h_listing_publish_status", "tableTo": "party", "columnsFrom": ["status_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.h_listing_stats": {"name": "h_listing_stats", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "listing_id": {"name": "listing_id", "type": "integer", "primaryKey": false, "notNull": true}, "stats_type": {"name": "stats_type", "type": "listing_stats_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"h_listing_stats_listing_id_h_listings_id_fk": {"name": "h_listing_stats_listing_id_h_listings_id_fk", "tableFrom": "h_listing_stats", "tableTo": "h_listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.h_listings": {"name": "h_listings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "source_type": {"name": "source_type", "type": "listing_source_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "source_id": {"name": "source_id", "type": "integer", "primaryKey": false, "notNull": true}, "listing_type": {"name": "listing_type", "type": "listing_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "effective_from": {"name": "effective_from", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "effective_to": {"name": "effective_to", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "'infinity'::timestamp"}, "listing_details": {"name": "listing_details", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"h_listings_party_id_party_id_fk": {"name": "h_listings_party_id_party_id_fk", "tableFrom": "h_listings", "tableTo": "party", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.h_vehicle_catalog": {"name": "h_vehicle_catalog", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "make": {"name": "make", "type": "text", "primaryKey": false, "notNull": true}, "model": {"name": "model", "type": "text", "primaryKey": false, "notNull": true}, "year": {"name": "year", "type": "integer", "primaryKey": false, "notNull": true}, "variant": {"name": "variant", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "fuel_type": {"name": "fuel_type", "type": "text", "primaryKey": false, "notNull": true}, "ehailing_eligible": {"name": "ehailing_eligible", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "estimated_price": {"name": "estimated_price", "type": "double precision", "primaryKey": false, "notNull": false}, "weekly_fee_target": {"name": "weekly_fee_target", "type": "double precision", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "specifications": {"name": "specifications", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.h_vehicle_catalog_features": {"name": "h_vehicle_catalog_features", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "catalog_id": {"name": "catalog_id", "type": "integer", "primaryKey": false, "notNull": true}, "feature": {"name": "feature", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"h_vehicle_catalog_features_catalog_id_h_vehicle_catalog_id_fk": {"name": "h_vehicle_catalog_features_catalog_id_h_vehicle_catalog_id_fk", "tableFrom": "h_vehicle_catalog_features", "tableTo": "h_vehicle_catalog", "columnsFrom": ["catalog_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.h_vehicle_catalog_images": {"name": "h_vehicle_catalog_images", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "catalog_id": {"name": "catalog_id", "type": "integer", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"h_vehicle_catalog_images_catalog_id_h_vehicle_catalog_id_fk": {"name": "h_vehicle_catalog_images_catalog_id_h_vehicle_catalog_id_fk", "tableFrom": "h_vehicle_catalog_images", "tableTo": "h_vehicle_catalog", "columnsFrom": ["catalog_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.h_vehicle_catalog_platforms": {"name": "h_vehicle_catalog_platforms", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "catalog_id": {"name": "catalog_id", "type": "integer", "primaryKey": false, "notNull": true}, "platform": {"name": "platform", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"h_vehicle_catalog_platforms_catalog_id_h_vehicle_catalog_id_fk": {"name": "h_vehicle_catalog_platforms_catalog_id_h_vehicle_catalog_id_fk", "tableFrom": "h_vehicle_catalog_platforms", "tableTo": "h_vehicle_catalog", "columnsFrom": ["catalog_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.application_status": {"name": "application_status", "schema": "public", "values": ["pending", "under_review", "approved", "rejected", "withdrawn"]}, "public.application_type": {"name": "application_type", "schema": "public", "values": ["CO_OWNERSHIP", "LEASE"]}, "public.audienceenum": {"name": "audienceenum", "schema": "public", "values": ["BUSINESS", "E_HAILING", "CONSUMER"]}, "public.body_type": {"name": "body_type", "schema": "public", "values": ["sedan", "hatchback", "suv", "truck", "coupe", "convertible", "wagon", "van", "minivan", "other"]}, "public.booking_status": {"name": "booking_status", "schema": "public", "values": ["DRAFT", "PENDING", "CONFIRMED", "IN_PROGRESS", "COMPLETED", "CANCELLED", "NO_SHOW", "DISPUTED"]}, "public.bookingstatus": {"name": "bookingstatus", "schema": "public", "values": ["PENDING", "CONFIRMED", "CANCELLED", "COMPLETED"]}, "public.cleanlinesslevel": {"name": "cleanlinesslevel", "schema": "public", "values": ["clean", "acceptable", "dirty"]}, "public.commentpriority": {"name": "commentpriority", "schema": "public", "values": ["LOW", "MEDIUM", "HIGH"]}, "public.commentstatus": {"name": "commentstatus", "schema": "public", "values": ["OPEN", "RESOLVED", "DELETED"]}, "public.company_purpose": {"name": "company_purpose", "schema": "public", "values": ["RIDE_SHARE", "GROUP_MONETIZATION", "FLEET", "OTHER"]}, "public.companyownershipinviteenum": {"name": "companyownershipinviteenum", "schema": "public", "values": ["SENT", "DECLINED", "ACCEPTED"]}, "public.companytypeenum": {"name": "companytypeenum", "schema": "public", "values": ["PRIVATE_COMPANY", "NON_PROFIT", "PARTNERSHIP", "COOPERATIVE"]}, "public.compliance_category": {"name": "compliance_category", "schema": "public", "values": ["IDENTITY", "DRIVING", "FINANCIAL", "ADDRESS", "VEHICLE", "BUSINESS", "VERIFICATION", "OTHER"]}, "public.condition_enhanced": {"name": "condition_enhanced", "schema": "public", "values": ["excellent", "good", "fair", "poor"]}, "public.conditionenum": {"name": "conditionenum", "schema": "public", "values": ["new", "used"]}, "public.conditionlevel": {"name": "conditionlevel", "schema": "public", "values": ["none", "minor", "major"]}, "public.dashboardcondition": {"name": "dashboardcondition", "schema": "public", "values": ["working", "partial", "issues"]}, "public.disputestatus": {"name": "disputestatus", "schema": "public", "values": ["OPEN", "RESOLVED", "DELETED"]}, "public.disputetype": {"name": "disputetype", "schema": "public", "values": ["BOOKING", "vEEHICLE_DAMAGE", "VEHICLE_MAINTENANCE", "MAINTENANCE_COST_DISPUTE", "OTHER"]}, "public.document_status": {"name": "document_status", "schema": "public", "values": ["pending", "uploaded", "verified", "rejected"]}, "public.documenttype": {"name": "documenttype", "schema": "public", "values": ["registration", "insurance", "inspection", "other"]}, "public.drivetrain": {"name": "drivetrain", "schema": "public", "values": ["fwd", "rwd", "awd", "4wd", "other"]}, "public.duration_type": {"name": "duration_type", "schema": "public", "values": ["TERM", "INFINITE"]}, "public.finance_method": {"name": "finance_method", "schema": "public", "values": ["CASH", "FINANCE", "CASH_AND_FINANCE"]}, "public.fuel_type": {"name": "fuel_type", "schema": "public", "values": ["petrol", "diesel", "electric", "hybrid", "gas", "other"]}, "public.generalcondition": {"name": "generalcondition", "schema": "public", "values": ["good", "fair", "poor"]}, "public.group_role": {"name": "group_role", "schema": "public", "values": ["ADMIN", "MEMBER"]}, "public.handover_status": {"name": "handover_status", "schema": "public", "values": ["SCHEDULED", "IN_PROGRESS", "COMPLETED", "FAILED", "DISPUTED"]}, "public.handover_type": {"name": "handover_type", "schema": "public", "values": ["BOOKING_START", "BOOKING_END", "OWNERSHIP_TRANSFER", "MAINTENANCE_DROP", "MAINTENANCE_PICKUP"]}, "public.inspection_status": {"name": "inspection_status", "schema": "public", "values": ["SCHEDULED", "IN_PROGRESS", "COMPLETED", "DISPUTED", "CANCELLED"]}, "public.inspection_type": {"name": "inspection_type", "schema": "public", "values": ["PRE_HANDOVER", "POST_HANDOVER", "PERIODIC", "INCIDENT", "MAINTENANCE"]}, "public.invitation_status": {"name": "invitation_status", "schema": "public", "values": ["PENDING", "SENT", "ACCEPTED", "DECLINED", "CANCELLED", "EXPIRED"]}, "public.issue_type": {"name": "issue_type", "schema": "public", "values": ["CONDITION_DISPUTE", "NO_SHOW", "LATE_ARRIVAL", "MISSING_ITEMS", "DAMAGE_CLAIM", "OTHER"]}, "public.lightscondition": {"name": "lightscondition", "schema": "public", "values": ["working", "partial", "broken"]}, "public.listingtypeenum": {"name": "listingtypeenum", "schema": "public", "values": ["SHORT_TERM_LEASE_OUT", "LONG_TERM_LEASE_OUT", "CO_OWNERSHIP_SALE"]}, "public.odorlevel": {"name": "odorlevel", "schema": "public", "values": ["none", "mild", "strong"]}, "public.offer_status": {"name": "offer_status", "schema": "public", "values": ["DRAFT", "ACTIVE", "PAUSED", "EXPIRED", "WITHDRAWN"]}, "public.offer_type": {"name": "offer_type", "schema": "public", "values": ["CO_OWNERSHIP", "LEASE", "RENTAL", "FULL_SALE"]}, "public.party_status_enum": {"name": "party_status_enum", "schema": "public", "values": ["ACTIVE", "INACTIVE", "SUSPENDED", "DELETED"]}, "public.permission_type": {"name": "permission_type", "schema": "public", "values": ["OWNER", "AUTHORIZED_USER", "GROUP_MEMBER"]}, "public.photo_type_enhanced": {"name": "photo_type_enhanced", "schema": "public", "values": ["left_view", "right_view", "rear_view", "front_view", "dashboard", "seats_view", "interior", "odometer", "damage_detail", "tires", "inspector_signature"]}, "public.phototype": {"name": "phototype", "schema": "public", "values": ["left_view", "right_view", "rear_view", "front_view", "dashboard", "seats_view", "interior", "additional", "tires", "signature"]}, "public.possession_type": {"name": "possession_type", "schema": "public", "values": ["OWNER", "RENTER", "BORROWER", "MAINTENANCE"]}, "public.possessionstatus": {"name": "possessionstatus", "schema": "public", "values": ["PENDING", "COMPLETED", "CANCELLED"]}, "public.priority": {"name": "priority", "schema": "public", "values": ["LOW", "MEDIUM", "HIGH"]}, "public.group_purpose": {"name": "group_purpose", "schema": "public", "values": ["RIDE_SHARE", "FLEET_MANAGEMENT", "CO_OWNERSHIP", "SOCIAL", "OTHER"]}, "public.referencetypeenum": {"name": "referencetypeenum", "schema": "public", "values": ["TAX_PIN", "URL"]}, "public.requirement_status": {"name": "requirement_status", "schema": "public", "values": ["ACTIVE", "INACTIVE", "DEPRECATED"]}, "public.requirement_type": {"name": "requirement_type", "schema": "public", "values": ["DOCUMENT", "CERTIFIED_DOCUMENT", "IMAGE", "VIDEO", "AUDIO", "CERTIFICATION", "ATTESTATION", "INSPECTION", "AUDIT", "SITE_INSPECTION", "VEHICLE_INSPECTION", "OTP", "OTHER"]}, "public.risk_level": {"name": "risk_level", "schema": "public", "values": ["LOW", "MEDIUM", "HIGH"]}, "public.servicestatus": {"name": "servicestatus", "schema": "public", "values": ["SCHEDULED", "PENDING", "COMPLETED"]}, "public.servicetypeenum": {"name": "servicetypeenum", "schema": "public", "values": ["MAINTENANCE", "INSURANCE", "CLEANING", "FUEL", "OTHER"]}, "public.status": {"name": "status", "schema": "public", "values": ["DRAFT", "SUBMITTED", "UNDER_REVIEW", "ADDITIONAL_INFO_REQUIRED", "VERIFIED", "REJECTED", "EXPIRED", "PENDING_UPLOAD", "UPLOADED", "PENDING_VERIFICATION", "VERIFICATION_FAILED", "DOCUMENT_REJECTED", "DOCUMENT_ACCEPTED", "VERIFICATION_PENDING", "VERIFICATION_IN_PROGRESS", "VERIFICATION_COMPLETED"]}, "public.submission_status": {"name": "submission_status", "schema": "public", "values": ["DRAFT", "SUBMITTED", "UNDER_REVIEW", "ADDITIONAL_INFO_REQUIRED", "VERIFIED", "REJECTED", "EXPIRED"]}, "public.transmission": {"name": "transmission", "schema": "public", "values": ["automatic", "manual", "cvt", "dual_clutch", "other"]}, "public.vehicle_fuel_type": {"name": "vehicle_fuel_type", "schema": "public", "values": ["petrol", "diesel", "electric", "hybrid", "plug-in-hybrid"]}, "public.vehicle_transmission": {"name": "vehicle_transmission", "schema": "public", "values": ["manual", "automatic", "semi-automatic"]}, "public.vehicleservicestatus": {"name": "vehicleservicestatus", "schema": "public", "values": ["SCHEDULED", "PENDING", "COMPLETED"]}, "public.verification_method": {"name": "verification_method", "schema": "public", "values": ["MANUAL", "API", "AI", "BLOCKCHAIN"]}, "public.verification_status": {"name": "verification_status", "schema": "public", "values": ["PENDING", "IN_PROGRESS", "COMPLETED", "FAILED"]}, "public.verificationtypeenum": {"name": "verificationtypeenum", "schema": "public", "values": ["AI", "MANUAL", "API"]}, "public.listing_approval_status": {"name": "listing_approval_status", "schema": "public", "values": ["pending", "under_review", "approved", "rejected", "withdrawn"]}, "public.listing_publish_status": {"name": "listing_publish_status", "schema": "public", "values": ["pending", "published", "archived"]}, "public.listing_source_type": {"name": "listing_source_type", "schema": "public", "values": ["catalog", "vehicle"]}, "public.listing_stats_type": {"name": "listing_stats_type", "schema": "public", "values": ["views", "applications"]}, "public.listing_status": {"name": "listing_status", "schema": "public", "values": ["draft", "published", "archived"]}, "public.listing_type": {"name": "listing_type", "schema": "public", "values": ["rental", "fractional", "ehailing-platform"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {"public.current_booking_status_view": {"columns": {"booking_reference": {"name": "booking_reference", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "borrower_party_id": {"name": "borrower_party_id", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "booking_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "requested_start": {"name": "requested_start", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "requested_end": {"name": "requested_end", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "confirmed_start": {"name": "confirmed_start", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "confirmed_end": {"name": "confirmed_end", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "actual_start": {"name": "actual_start", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "actual_end": {"name": "actual_end", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "quoted_price": {"name": "quoted_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "final_price": {"name": "final_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "event_timestamp": {"name": "event_timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "definition": "select \"booking_reference\", \"vehicle_id\", \"borrower_party_id\", \"status\", \"requested_start\", \"requested_end\", \"confirmed_start\", \"confirmed_end\", \"actual_start\", \"actual_end\", \"quoted_price\", \"final_price\", \"event_timestamp\" from \"booking_events\" where \"booking_events\".\"id\" IN (\n        SELECT MAX(id) \n        FROM \"booking_events\" be2 \n        WHERE be2.booking_reference = \"booking_events\".\"booking_reference\"\n      )", "name": "current_booking_status_view", "schema": "public", "isExisting": false, "materialized": false}, "public.current_handover_status_view": {"columns": {"handover_id": {"name": "handover_id", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "handover_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status_timestamp": {"name": "status_timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "changed_by": {"name": "changed_by", "type": "integer", "primaryKey": false, "notNull": true}}, "definition": "select \"handover_id\", \"status\", \"status_timestamp\", \"changed_by\" from \"handover_status_events\" where \"handover_status_events\".\"id\" IN (\n        SELECT MAX(id)\n        FROM \"handover_status_events\" hse2\n        WHERE hse2.handover_id = \"handover_status_events\".\"handover_id\"\n      )", "name": "current_handover_status_view", "schema": "public", "isExisting": false, "materialized": false}, "public.current_vehicle_access_view": {"columns": {"vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "permission_type": {"name": "permission_type", "type": "permission_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "effective_from": {"name": "effective_from", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "granted_by": {"name": "granted_by", "type": "integer", "primaryKey": false, "notNull": true}}, "definition": "select \"vehicle_id\", \"party_id\", \"permission_type\", \"effective_from\", \"granted_by\" from \"vehicle_access_events\" where \"vehicle_access_events\".\"event_type\" = 'GRANTED'\n          AND \"vehicle_access_events\".\"effective_from\" <= NOW()\n          AND (\"vehicle_access_events\".\"effective_to\" IS NULL OR \"vehicle_access_events\".\"effective_to\" > NOW())\n          AND NOT EXISTS (\n            SELECT 1 FROM \"vehicle_access_events\" vae2\n            WHERE vae2.vehicle_id = \"vehicle_access_events\".\"vehicle_id\"\n            AND vae2.party_id = \"vehicle_access_events\".\"party_id\"\n            AND vae2.event_type = 'REVOKED'\n            AND vae2.event_timestamp > \"vehicle_access_events\".\"event_timestamp\"\n          )", "name": "current_vehicle_access_view", "schema": "public", "isExisting": false, "materialized": false}, "public.current_vehicle_possession_view": {"columns": {"vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "possessor_party_id": {"name": "possessor_party_id", "type": "integer", "primaryKey": false, "notNull": true}, "possession_type": {"name": "possession_type", "type": "possession_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "possession_start": {"name": "possession_start", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "trigger_type": {"name": "trigger_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "trigger_reference": {"name": "trigger_reference", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "definition": "select \"vehicle_id\", \"possessor_party_id\", \"possession_type\", \"possession_start\", \"trigger_type\", \"trigger_reference\" from \"vehicle_possession_events\" where \"vehicle_possession_events\".\"possession_end\" IS NULL \n          AND \"vehicle_possession_events\".\"id\" IN (\n            SELECT MAX(id) \n            FROM \"vehicle_possession_events\" vpe2 \n            WHERE vpe2.vehicle_id = \"vehicle_possession_events\".\"vehicle_id\"\n            AND vpe2.possession_end IS NULL\n          )", "name": "current_vehicle_possession_view", "schema": "public", "isExisting": false, "materialized": false}, "public.groups_community_view": {"columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": true}, "is_managed": {"name": "is_managed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "initial_purpose": {"name": "initial_purpose", "type": "company_purpose", "primaryKey": false, "notNull": true}}, "definition": "select \"groups\".\"id\", \"groups\".\"name\", \"groups\".\"description\", \"groups\".\"created_at\", \"groups\".\"created_by\", \"cities\".\"name\" as \"city_name\", \"countries\".\"name\" as \"country_name\", \"groups\".\"is_managed\", \"groups\".\"initial_purpose\", cast(count(distinct \"group_memberships\".\"party_id\") as integer) as \"member_count\", cast(count(distinct \"group_shared_vehicles\".\"vehicle_id\") as integer) as \"vehicle_count\" from \"groups\" left join \"cities\" on \"groups\".\"city_id\" = \"cities\".\"id\" left join \"countries\" on \"groups\".\"country_id\" = \"countries\".\"id\" left join \"group_memberships\" on \"groups\".\"id\" = \"group_memberships\".\"group_id\" AND \"group_memberships\".\"effective_from\" <= NOW() AND (\"group_memberships\".\"effective_to\" IS NULL OR \"group_memberships\".\"effective_to\" > NOW()) left join \"group_shared_vehicles\" on \"groups\".\"id\" = \"group_shared_vehicles\".\"group_id\" AND \"group_shared_vehicles\".\"is_active\" = true AND \"group_shared_vehicles\".\"effective_from\" <= NOW() AND (\"group_shared_vehicles\".\"effective_to\" IS NULL OR \"group_shared_vehicles\".\"effective_to\" > NOW()) group by \"groups\".\"id\", \"groups\".\"name\", \"groups\".\"description\", \"groups\".\"created_at\", \"groups\".\"created_by\", \"cities\".\"name\", \"countries\".\"name\", \"groups\".\"is_managed\", \"groups\".\"initial_purpose\"", "name": "groups_community_view", "schema": "public", "isExisting": false, "materialized": false}, "public.user_group_memberships_view": {"columns": {"group_id": {"name": "group_id", "type": "integer", "primaryKey": false, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "group_role", "primaryKey": false, "notNull": true}}, "definition": "select \"group_memberships\".\"group_id\", \"group_memberships\".\"party_id\", \"group_member_roles\".\"role\", \"groups\".\"name\" as \"group_name\", \"groups\".\"description\" as \"group_description\", \"cities\".\"name\" as \"city_name\", \"countries\".\"name\" as \"country_name\", \"groups\".\"created_at\" as \"group_created_at\", \"group_memberships\".\"effective_from\" as \"membership_start\", \"group_memberships\".\"effective_to\" as \"membership_end\", \"group_member_roles\".\"effective_from\" as \"role_start\", \"group_member_roles\".\"effective_to\" as \"role_end\", \"groups\".\"is_managed\" as \"is_managed\", \"groups\".\"initial_purpose\" as \"initial_purpose\" from \"group_memberships\" inner join \"groups\" on \"group_memberships\".\"group_id\" = \"groups\".\"id\" left join \"group_member_roles\" on \"group_memberships\".\"group_id\" = \"group_member_roles\".\"group_id\" AND \"group_memberships\".\"party_id\" = \"group_member_roles\".\"party_id\" AND \"group_member_roles\".\"effective_from\" <= NOW() AND (\"group_member_roles\".\"effective_to\" IS NULL OR \"group_member_roles\".\"effective_to\" > NOW()) left join \"cities\" on \"groups\".\"city_id\" = \"cities\".\"id\" left join \"countries\" on \"groups\".\"country_id\" = \"countries\".\"id\" where \"group_memberships\".\"effective_from\" <= NOW() AND (\"group_memberships\".\"effective_to\" IS NULL OR \"group_memberships\".\"effective_to\" > NOW())", "name": "user_group_memberships_view", "schema": "public", "isExisting": false, "materialized": false}, "public.groups_detailed_view": {"columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "city_id": {"name": "city_id", "type": "integer", "primaryKey": false, "notNull": false}, "country_id": {"name": "country_id", "type": "integer", "primaryKey": false, "notNull": false}, "initial_purpose": {"name": "initial_purpose", "type": "company_purpose", "primaryKey": false, "notNull": true}, "is_managed": {"name": "is_managed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": true}, "creator": {"name": "creator", "type": "integer", "primaryKey": false, "notNull": true}}, "definition": "select \"groups\".\"id\", \"groups\".\"party_id\", \"groups\".\"name\", \"groups\".\"description\", \"groups\".\"city_id\", \"groups\".\"country_id\", \"groups\".\"initial_purpose\", \"groups\".\"is_managed\", \"groups\".\"created_at\", \"groups\".\"created_by\", \"groups\".\"creator\", \"cities\".\"name\" as \"city_name\", \"countries\".\"name\" as \"country_name\", \"province_state\".\"name\" as \"province_state_name\", \"individual\".\"first_name\" as \"creator_first_name\", \"individual\".\"last_name\" as \"creator_last_name\", cast(count(distinct \"group_memberships\".\"party_id\") as integer) as \"member_count\", cast(count(distinct \"group_shared_vehicles\".\"vehicle_id\") as integer) as \"vehicle_count\", cast(count(distinct case when \"group_member_roles\".\"role\" = 'ADMIN' then \"group_member_roles\".\"party_id\" end) as integer) as \"admin_count\" from \"groups\" left join \"cities\" on \"groups\".\"city_id\" = \"cities\".\"id\" left join \"countries\" on \"groups\".\"country_id\" = \"countries\".\"id\" left join \"province_state\" on \"cities\".\"province_state_id\" = \"province_state\".\"id\" left join \"party\" on \"groups\".\"creator\" = \"party\".\"id\" left join \"individual\" on \"party\".\"id\" = \"individual\".\"party_id\" left join \"group_memberships\" on \"groups\".\"id\" = \"group_memberships\".\"group_id\" AND \"group_memberships\".\"effective_from\" <= NOW() AND (\"group_memberships\".\"effective_to\" IS NULL OR \"group_memberships\".\"effective_to\" > NOW()) left join \"group_shared_vehicles\" on \"groups\".\"id\" = \"group_shared_vehicles\".\"group_id\" AND \"group_shared_vehicles\".\"is_active\" = true AND \"group_shared_vehicles\".\"effective_from\" <= NOW() AND (\"group_shared_vehicles\".\"effective_to\" IS NULL OR \"group_shared_vehicles\".\"effective_to\" > NOW()) left join \"group_member_roles\" on \"groups\".\"id\" = \"group_member_roles\".\"group_id\" AND \"group_member_roles\".\"effective_from\" <= NOW() AND (\"group_member_roles\".\"effective_to\" IS NULL OR \"group_member_roles\".\"effective_to\" > NOW()) group by \"groups\".\"id\", \"groups\".\"party_id\", \"groups\".\"name\", \"groups\".\"description\", \"groups\".\"city_id\", \"groups\".\"country_id\", \"groups\".\"initial_purpose\", \"groups\".\"is_managed\", \"groups\".\"created_at\", \"groups\".\"created_by\", \"groups\".\"creator\", \"cities\".\"name\", \"countries\".\"name\", \"province_state\".\"name\", \"individual\".\"first_name\", \"individual\".\"last_name\"", "name": "groups_detailed_view", "schema": "public", "isExisting": false, "materialized": true}}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}