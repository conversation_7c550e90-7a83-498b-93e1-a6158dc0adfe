DO $$ BEGIN
 CREATE TYPE "public"."listing_approval_status" AS ENUM('pending', 'under_review', 'approved', 'rejected', 'withdrawn');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "public"."listing_publish_status" AS ENUM('pending', 'published', 'archived');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "public"."listing_source_type" AS ENUM('catalog', 'vehicle');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "public"."listing_stats_type" AS ENUM('views', 'applications');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "public"."listing_status" AS ENUM('draft', 'published', 'archived');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "public"."listing_type" AS ENUM('rental', 'fractional', 'ehailing-platform');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint
CREATE TABLE "h_application_decisions" (
	"id" serial PRIMARY KEY NOT NULL,
	"decision" "application_status" NOT NULL,
	"reason" text,
	"application_id" integer NOT NULL,
	"reviewer_id" integer NOT NULL,
	"decision_at" timestamp with time zone NOT NULL
);
--> statement-breakpoint
CREATE TABLE "h_application_documents" (
	"id" serial PRIMARY KEY NOT NULL,
	"application_id" integer NOT NULL,
	"documentType" varchar NOT NULL,
	"documentUrl" varchar NOT NULL,
	"uploaded_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "h_application_documents_status" (
	"id" serial PRIMARY KEY NOT NULL,
	"application_document_id" integer NOT NULL,
	"status" "document_status" NOT NULL,
	"status_at" timestamp with time zone NOT NULL,
	"status_by" integer
);
--> statement-breakpoint
CREATE TABLE "h_applications" (
	"id" serial PRIMARY KEY NOT NULL,
	"applicant_id" integer NOT NULL,
	"listing_id" integer NOT NULL,
	"application_details" text
);
--> statement-breakpoint
CREATE TABLE "h_listing_approval_status" (
	"id" serial PRIMARY KEY NOT NULL,
	"listing_id" integer NOT NULL,
	"status" "listing_approval_status" NOT NULL,
	"reason" text,
	"status_at" timestamp with time zone NOT NULL,
	"status_by" integer
);
--> statement-breakpoint
CREATE TABLE "h_listing_publish_status" (
	"id" serial PRIMARY KEY NOT NULL,
	"listing_id" integer NOT NULL,
	"status" "listing_publish_status" NOT NULL,
	"status_at" timestamp with time zone NOT NULL,
	"status_by" integer
);
--> statement-breakpoint
CREATE TABLE "h_listing_stats" (
	"id" serial PRIMARY KEY NOT NULL,
	"listing_id" integer NOT NULL,
	"stats_type" "listing_stats_type" NOT NULL,
	"created_at" timestamp with time zone NOT NULL,
	"party_id" integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE "h_listings" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"source_type" "listing_source_type" NOT NULL,
	"source_id" integer NOT NULL,
	"listing_type" "listing_type" NOT NULL,
	"effective_from" timestamp with time zone NOT NULL,
	"effective_to" timestamp with time zone DEFAULT 'infinity'::timestamp,
	"listing_details" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "h_vehicle_catalog" (
	"id" serial PRIMARY KEY NOT NULL,
	"make" text NOT NULL,
	"model" text NOT NULL,
	"year" integer NOT NULL,
	"variant" text,
	"category" text NOT NULL,
	"fuel_type" text NOT NULL,
	"ehailing_eligible" boolean DEFAULT false NOT NULL,
	"estimated_price" double precision,
	"weekly_fee_target" double precision,
	"description" text,
	"specifications" text,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "h_vehicle_catalog_features" (
	"id" serial PRIMARY KEY NOT NULL,
	"catalog_id" integer NOT NULL,
	"feature" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "h_vehicle_catalog_images" (
	"id" serial PRIMARY KEY NOT NULL,
	"catalog_id" integer NOT NULL,
	"image_url" text NOT NULL,
	"is_primary" boolean DEFAULT false NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "h_vehicle_catalog_platforms" (
	"id" serial PRIMARY KEY NOT NULL,
	"catalog_id" integer NOT NULL,
	"platform" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "applications" ALTER COLUMN "status" SET DATA TYPE text;--> statement-breakpoint
ALTER TABLE "h_application_decisions" ALTER COLUMN "decision" SET DATA TYPE text;--> statement-breakpoint
DROP TYPE "public"."application_status";--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "public"."application_status" AS ENUM('pending', 'under_review', 'approved', 'rejected', 'withdrawn');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint
ALTER TABLE "applications" ALTER COLUMN "status" SET DATA TYPE "public"."application_status" USING "status"::"public"."application_status";--> statement-breakpoint
ALTER TABLE "h_application_decisions" ALTER COLUMN "decision" SET DATA TYPE "public"."application_status" USING "decision"::"public"."application_status";--> statement-breakpoint
ALTER TABLE "compliance_document" ALTER COLUMN "status" SET DATA TYPE text;--> statement-breakpoint
ALTER TABLE "h_application_documents_status" ALTER COLUMN "status" SET DATA TYPE text;--> statement-breakpoint
DROP TYPE "public"."document_status";--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "public"."document_status" AS ENUM('pending', 'uploaded', 'verified', 'rejected');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint
ALTER TABLE "compliance_document" ALTER COLUMN "status" SET DATA TYPE "public"."document_status" USING "status"::"public"."document_status";--> statement-breakpoint
ALTER TABLE "h_application_documents_status" ALTER COLUMN "status" SET DATA TYPE "public"."document_status" USING "status"::"public"."document_status";--> statement-breakpoint
ALTER TABLE "h_application_decisions" ADD CONSTRAINT "h_application_decisions_application_id_h_applications_id_fk" FOREIGN KEY ("application_id") REFERENCES "public"."h_applications"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_application_decisions" ADD CONSTRAINT "h_application_decisions_reviewer_id_users_id_fk" FOREIGN KEY ("reviewer_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_application_documents" ADD CONSTRAINT "h_application_documents_application_id_h_applications_id_fk" FOREIGN KEY ("application_id") REFERENCES "public"."h_applications"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_application_documents_status" ADD CONSTRAINT "h_application_documents_status_application_document_id_h_application_documents_id_fk" FOREIGN KEY ("application_document_id") REFERENCES "public"."h_application_documents"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_application_documents_status" ADD CONSTRAINT "h_application_documents_status_status_by_party_id_fk" FOREIGN KEY ("status_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_applications" ADD CONSTRAINT "h_applications_applicant_id_party_id_fk" FOREIGN KEY ("applicant_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_applications" ADD CONSTRAINT "h_applications_listing_id_h_listings_id_fk" FOREIGN KEY ("listing_id") REFERENCES "public"."h_listings"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_listing_approval_status" ADD CONSTRAINT "h_listing_approval_status_listing_id_h_listings_id_fk" FOREIGN KEY ("listing_id") REFERENCES "public"."h_listings"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_listing_approval_status" ADD CONSTRAINT "h_listing_approval_status_status_by_party_id_fk" FOREIGN KEY ("status_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_listing_publish_status" ADD CONSTRAINT "h_listing_publish_status_listing_id_h_listings_id_fk" FOREIGN KEY ("listing_id") REFERENCES "public"."h_listings"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_listing_publish_status" ADD CONSTRAINT "h_listing_publish_status_status_by_party_id_fk" FOREIGN KEY ("status_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_listing_stats" ADD CONSTRAINT "h_listing_stats_listing_id_h_listings_id_fk" FOREIGN KEY ("listing_id") REFERENCES "public"."h_listings"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_listings" ADD CONSTRAINT "h_listings_party_id_party_id_fk" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_vehicle_catalog_features" ADD CONSTRAINT "h_vehicle_catalog_features_catalog_id_h_vehicle_catalog_id_fk" FOREIGN KEY ("catalog_id") REFERENCES "public"."h_vehicle_catalog"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_vehicle_catalog_images" ADD CONSTRAINT "h_vehicle_catalog_images_catalog_id_h_vehicle_catalog_id_fk" FOREIGN KEY ("catalog_id") REFERENCES "public"."h_vehicle_catalog"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_vehicle_catalog_platforms" ADD CONSTRAINT "h_vehicle_catalog_platforms_catalog_id_h_vehicle_catalog_id_fk" FOREIGN KEY ("catalog_id") REFERENCES "public"."h_vehicle_catalog"("id") ON DELETE cascade ON UPDATE no action;