"use client";

import React, { useState } from "react";
import {
  ArrowLeft,
  User,
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  Car,
  Phone,
  Mail,
  AlertTriangle,
  Download,
  Eye,
  Star,
} from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ListingApprovalData } from "@/drizzle-actions/admin/listings";

interface ApprovalReviewPageClientProps {
  approvalData: ListingApprovalData;
}

export default function ApprovalReviewPageClient({
  approvalData,
}: ApprovalReviewPageClientProps) {
  const [currentTab, setCurrentTab] = useState("overview");
  const [decisionReason, setDecisionReason] = useState("");
  const [isDecisionDialogOpen, setIsDecisionDialogOpen] = useState(false);
  const [pendingDecision, setPendingDecision] = useState<
    "approved" | "rejected" | null
  >(null);
  const [documentStatuses, setDocumentStatuses] = useState<
    Record<string, boolean | "failed">
  >({});

  // Parse listing details JSON
  const listingDetails = approvalData.listingDetails
    ? JSON.parse(approvalData.listingDetails)
    : {};

  // Helper function to format dates consistently
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatShortDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "under_review":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "approved":
        return "bg-green-100 text-green-800 border-green-200";
      case "rejected":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const handleDecision = (decision: "approved" | "rejected") => {
    setPendingDecision(decision);
    setIsDecisionDialogOpen(true);
  };

  const confirmDecision = async () => {
    if (!pendingDecision) return;

    try {
      // TODO: Implement actual approval/rejection logic
      console.log(
        `${pendingDecision} listing ${approvalData.id} with reason: ${decisionReason}`
      );

      setIsDecisionDialogOpen(false);
      setPendingDecision(null);
      setDecisionReason("");

      // TODO: Refresh data or redirect
    } catch (error) {
      console.error("Error processing decision:", error);
    }
  };

  // Mock documents for now - in real implementation, get from listing details or separate table
  const mockDocuments = [
    {
      name: "Vehicle Registration",
      uploaded: true,
      verified: true,
      fileUrl: "/docs/registration.pdf",
    },
    {
      name: "Driver's License",
      uploaded: true,
      verified: false,
      fileUrl: "/docs/license.pdf",
    },
    {
      name: "Insurance Certificate",
      uploaded: true,
      verified: true,
      fileUrl: "/docs/insurance.pdf",
    },
    {
      name: "Vehicle Photos",
      uploaded: true,
      verified: false,
      fileUrl: "/docs/photos.zip",
    },
  ];

  const handleDocumentVerification = (
    docName: string,
    status: boolean | "failed"
  ) => {
    setDocumentStatuses((prev) => ({
      ...prev,
      [docName]: status,
    }));
    console.log(`Document ${docName} verification:`, status);
  };

  const getDocumentVerificationStatus = (doc: (typeof mockDocuments)[0]) => {
    const overrideStatus = documentStatuses[doc.name];
    if (overrideStatus !== undefined) {
      return overrideStatus;
    }
    return doc.verified;
  };

  const getDocumentStatus = (doc: (typeof mockDocuments)[0]) => {
    const verificationStatus = getDocumentVerificationStatus(doc);

    if (!doc.uploaded)
      return {
        color: "text-gray-400",
        bgColor: "bg-gray-100",
        icon: <XCircle size={16} />,
        text: "Not uploaded",
      };
    if (verificationStatus === "failed")
      return {
        color: "text-red-600",
        bgColor: "bg-red-100",
        icon: <XCircle size={16} />,
        text: "Verification failed",
      };
    if (verificationStatus === false)
      return {
        color: "text-yellow-600",
        bgColor: "bg-yellow-100",
        icon: <Clock size={16} />,
        text: "Pending verification",
      };
    if (verificationStatus === true)
      return {
        color: "text-green-600",
        bgColor: "bg-green-100",
        icon: <CheckCircle size={16} />,
        text: "Verified",
      };
    return {
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      icon: <Eye size={16} />,
      text: "Under review",
    };
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/approvals">
              <ArrowLeft size={16} />
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">
              Vehicle Listing Review
            </h1>
            <p className="text-gray-600 mt-1">
              {approvalData.partyName} • {approvalData.sourceDetails.make}{" "}
              {approvalData.sourceDetails.model}{" "}
              {approvalData.sourceDetails.year}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Badge
            variant="outline"
            className={`${getStatusColor(approvalData.approvalStatus)} px-3 py-1`}
          >
            {approvalData.approvalStatus.replace("_", " ")}
          </Badge>
          {approvalData.approvalStatus === "pending" && (
            <div className="flex space-x-2">
              <Button
                variant="outline"
                className="text-red-600 border-red-200 hover:bg-red-50"
                onClick={() => handleDecision("rejected")}
              >
                <XCircle size={16} className="mr-2" />
                Reject
              </Button>
              <Button
                className="bg-[#009639] hover:bg-[#007A2F]"
                onClick={() => handleDecision("approved")}
              >
                <CheckCircle size={16} className="mr-2" />
                Approve
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Application Status Alert */}
      {approvalData.approvalStatus === "pending" && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center">
              <AlertTriangle className="text-yellow-600 mr-3" size={20} />
              <div>
                <p className="font-medium text-yellow-800">
                  Listing Pending Review
                </p>
                <p className="text-sm text-yellow-700">
                  This listing is waiting for your review and decision.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Tabs value={currentTab} onValueChange={setCurrentTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
              <TabsTrigger value="decision">Decision</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* User Profile */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User size={20} className="text-[#009639]" />
                    Vehicle Owner Profile
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Full Name
                      </Label>
                      <p className="text-lg font-medium">
                        {approvalData.partyName || "Unknown"}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Application Date
                      </Label>
                      <p className="text-lg">
                        {formatDate(approvalData.approvalStatusAt)}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Email Address
                      </Label>
                      <div className="flex items-center">
                        <Mail size={16} className="text-gray-400 mr-2" />
                        <p>
                          {approvalData.contactEmail ||
                            approvalData.partyEmail ||
                            "Not provided"}
                        </p>
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Phone Number
                      </Label>
                      <div className="flex items-center">
                        <Phone size={16} className="text-gray-400 mr-2" />
                        <p>{approvalData.contactPhone || "Not provided"}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Vehicle Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Car size={20} className="text-[#009639]" />
                    Vehicle Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Make & Model
                      </Label>
                      <p className="text-lg font-medium">
                        {approvalData.sourceDetails.make}{" "}
                        {approvalData.sourceDetails.model}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Year
                      </Label>
                      <p className="text-lg">
                        {approvalData.sourceDetails.year || "N/A"}
                      </p>
                    </div>
                    {approvalData.sourceDetails.registrationNumber && (
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Registration Number
                        </Label>
                        <p className="text-lg font-medium">
                          {approvalData.sourceDetails.registrationNumber}
                        </p>
                      </div>
                    )}
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Listing Type
                      </Label>
                      <p className="text-lg capitalize">
                        {approvalData.listingType}
                      </p>
                    </div>
                  </div>

                  {/* Financial Details from JSON */}
                  {Object.keys(listingDetails).length > 0 && (
                    <div className="border-t pt-4">
                      <Label className="text-sm font-medium text-gray-500 mb-2 block">
                        Financial Details
                      </Label>
                      <div className="grid grid-cols-2 gap-4">
                        {listingDetails.rate && (
                          <div>
                            <p className="text-sm text-gray-500">Rate</p>
                            <p className="font-semibold">
                              R{listingDetails.rate}
                            </p>
                          </div>
                        )}
                        {listingDetails.deposit && (
                          <div>
                            <p className="text-sm text-gray-500">Deposit</p>
                            <p className="font-semibold">
                              R{listingDetails.deposit}
                            </p>
                          </div>
                        )}
                        {listingDetails.fraction && (
                          <div>
                            <p className="text-sm text-gray-500">Fraction</p>
                            <p className="font-semibold">
                              {(listingDetails.fraction * 100).toFixed(1)}%
                            </p>
                          </div>
                        )}
                        {listingDetails.amount && (
                          <div>
                            <p className="text-sm text-gray-500">Amount</p>
                            <p className="font-semibold">
                              R{listingDetails.amount}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Preferences/Experience Card */}
              {approvalData.listingType === "ehailing-platform" && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Star size={20} className="text-[#009639]" />
                      E-hailing Experience
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Previous Experience
                        </Label>
                        <p className="text-lg">
                          {listingDetails.hasExperience ? "Yes" : "No"}
                        </p>
                      </div>
                      {listingDetails.company && (
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Company
                          </Label>
                          <p className="text-lg">{listingDetails.company}</p>
                        </div>
                      )}
                      {listingDetails.duration && (
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Duration
                          </Label>
                          <p className="text-lg">{listingDetails.duration}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="documents" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText size={20} className="text-[#009639]" />
                    Vehicle Documents
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockDocuments.map((doc, index) => {
                      const status = getDocumentStatus(doc);
                      return (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div
                                className={`p-2 rounded-full ${status.bgColor}`}
                              >
                                <div className={status.color}>
                                  {status.icon}
                                </div>
                              </div>
                              <div>
                                <h4 className="font-medium">{doc.name}</h4>
                                <p className={`text-sm ${status.color}`}>
                                  {status.text}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex items-center gap-1"
                              >
                                <Eye size={14} />
                                View
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex items-center gap-1"
                              >
                                <Download size={14} />
                                Download
                              </Button>
                            </div>
                          </div>

                          {/* Document Verification Actions */}
                          {doc.uploaded && (
                            <div className="mt-4 pt-4 border-t">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">
                                  Document Verification
                                </span>
                                <div className="flex items-center space-x-2">
                                  {/* Show verification actions only if not already verified */}
                                  {getDocumentVerificationStatus(doc) ===
                                    false && (
                                    <>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="flex items-center gap-1 text-green-600 border-green-200 hover:bg-green-50"
                                        onClick={() =>
                                          handleDocumentVerification(
                                            doc.name,
                                            true
                                          )
                                        }
                                      >
                                        <CheckCircle size={14} />
                                        Approve
                                      </Button>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="flex items-center gap-1 text-red-600 border-red-200 hover:bg-red-50"
                                        onClick={() =>
                                          handleDocumentVerification(
                                            doc.name,
                                            "failed"
                                          )
                                        }
                                      >
                                        <XCircle size={14} />
                                        Reject
                                      </Button>
                                    </>
                                  )}

                                  {/* Reset verification if already verified or failed */}
                                  {(getDocumentVerificationStatus(doc) ===
                                    true ||
                                    getDocumentVerificationStatus(doc) ===
                                      "failed") && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="flex items-center gap-1 text-yellow-600 border-yellow-200 hover:bg-yellow-50"
                                      onClick={() =>
                                        handleDocumentVerification(
                                          doc.name,
                                          false
                                        )
                                      }
                                    >
                                      <Clock size={14} />
                                      Reset
                                    </Button>
                                  )}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="decision" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle size={20} className="text-[#009639]" />
                    Listing Decision
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label
                      htmlFor="decision-reason"
                      className="text-sm font-medium"
                    >
                      Decision Reason (Optional)
                    </Label>
                    <Textarea
                      id="decision-reason"
                      value={decisionReason}
                      onChange={(e) => setDecisionReason(e.target.value)}
                      placeholder="Add a note about your decision..."
                      className="mt-2"
                      rows={4}
                    />
                  </div>

                  <div className="flex space-x-4">
                    <Button
                      variant="outline"
                      className="flex-1 text-red-600 border-red-200 hover:bg-red-50"
                      onClick={() => handleDecision("rejected")}
                    >
                      <XCircle size={16} className="mr-2" />
                      Reject Listing
                    </Button>
                    <Button
                      className="flex-1 bg-[#009639] hover:bg-[#007A2F]"
                      onClick={() => handleDecision("approved")}
                    >
                      <CheckCircle size={16} className="mr-2" />
                      Approve Listing
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Column - Summary & Timeline */}
        <div className="space-y-6">
          {/* Summary Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Status</span>
                <Badge
                  variant="outline"
                  className={`${getStatusColor(approvalData.approvalStatus)} text-xs`}
                >
                  {approvalData.approvalStatus.replace("_", " ")}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Documents</span>
                <span className="text-sm font-medium">
                  {mockDocuments.filter((doc) => doc.uploaded).length} /{" "}
                  {mockDocuments.length} uploaded
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Verified</span>
                <span className="text-sm font-medium">
                  {
                    mockDocuments.filter(
                      (doc) => getDocumentVerificationStatus(doc) === true
                    ).length
                  }{" "}
                  / {mockDocuments.filter((doc) => doc.uploaded).length}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Listing Type</span>
                <span className="text-sm font-medium capitalize">
                  {approvalData.listingType}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                variant="outline"
                className="w-full justify-start"
                size="sm"
              >
                <Mail size={16} className="mr-2" />
                Contact Owner
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                size="sm"
              >
                <Download size={16} className="mr-2" />
                Download All Documents
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                size="sm"
              >
                <FileText size={16} className="mr-2" />
                View Full Details
              </Button>
            </CardContent>
          </Card>

          {/* Timeline Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Listing Submitted</p>
                    <p className="text-xs text-gray-500">
                      {formatShortDate(approvalData.approvalStatusAt)}
                    </p>
                  </div>
                </div>

                {approvalData.approvalStatus === "under_review" && (
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Under Review</p>
                      <p className="text-xs text-gray-500">In progress</p>
                    </div>
                  </div>
                )}

                {approvalData.approvalStatus === "approved" && (
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Approved</p>
                      <p className="text-xs text-gray-500">
                        {formatShortDate(approvalData.approvalStatusAt)}
                      </p>
                      {approvalData.approvalReason && (
                        <p className="text-xs text-gray-600 mt-1">
                          {approvalData.approvalReason}
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {approvalData.approvalStatus === "rejected" && (
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Rejected</p>
                      <p className="text-xs text-gray-500">
                        {formatShortDate(approvalData.approvalStatusAt)}
                      </p>
                      {approvalData.approvalReason && (
                        <p className="text-xs text-gray-600 mt-1">
                          {approvalData.approvalReason}
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Decision Confirmation Dialog */}
      <Dialog
        open={isDecisionDialogOpen}
        onOpenChange={setIsDecisionDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {pendingDecision === "approved" ? "Approve" : "Reject"} Listing
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to{" "}
              {pendingDecision === "approved" ? "approve" : "reject"} this
              vehicle listing? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="reason" className="text-sm font-medium">
                Reason (Optional)
              </Label>
              <Textarea
                id="reason"
                value={decisionReason}
                onChange={(e) => setDecisionReason(e.target.value)}
                placeholder={`Add a note about why you ${
                  pendingDecision === "approved" ? "approved" : "rejected"
                } this listing...`}
                className="mt-2"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDecisionDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={confirmDecision}
              className={
                pendingDecision === "approved"
                  ? "bg-[#009639] hover:bg-[#007A2F]"
                  : "bg-red-600 hover:bg-red-700"
              }
            >
              {pendingDecision === "approved" ? "Approve" : "Reject"} Listing
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
