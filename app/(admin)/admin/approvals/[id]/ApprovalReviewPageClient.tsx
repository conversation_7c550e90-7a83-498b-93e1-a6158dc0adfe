"use client";

import React, { useState } from "react";
import {
  ArrowLeft,
  User,
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  Car,
  Phone,
  Mail,
  AlertTriangle,
  DollarSign,
  Calendar,
  MapPin,
} from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ListingApprovalData } from "@/drizzle-actions/admin/listings";

interface ApprovalReviewPageClientProps {
  approvalData: ListingApprovalData;
}

export default function ApprovalReviewPageClient({
  approvalData,
}: ApprovalReviewPageClientProps) {
  const [currentTab, setCurrentTab] = useState("overview");
  const [decisionReason, setDecisionReason] = useState("");
  const [isDecisionDialogOpen, setIsDecisionDialogOpen] = useState(false);
  const [pendingDecision, setPendingDecision] = useState<
    "approved" | "rejected" | null
  >(null);

  // Parse listing details JSON
  const listingDetails = approvalData.listingDetails
    ? JSON.parse(approvalData.listingDetails)
    : {};

  // Helper function to format dates consistently
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatShortDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-100 text-yellow-800", icon: Clock },
      under_review: { color: "bg-blue-100 text-blue-800", icon: FileText },
      approved: { color: "bg-green-100 text-green-800", icon: CheckCircle },
      rejected: { color: "bg-red-100 text-red-800", icon: XCircle },
      withdrawn: { color: "bg-gray-100 text-gray-800", icon: AlertTriangle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="w-3 h-3" />
        {status.replace("_", " ").toUpperCase()}
      </Badge>
    );
  };

  const handleDecision = (decision: "approved" | "rejected") => {
    setPendingDecision(decision);
    setIsDecisionDialogOpen(true);
  };

  const confirmDecision = async () => {
    if (!pendingDecision) return;

    try {
      // TODO: Implement actual approval/rejection logic
      console.log(`${pendingDecision} listing ${approvalData.id} with reason: ${decisionReason}`);
      
      setIsDecisionDialogOpen(false);
      setPendingDecision(null);
      setDecisionReason("");
      
      // TODO: Refresh data or redirect
    } catch (error) {
      console.error("Error processing decision:", error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/admin/approvals"
                className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Back to Approvals
              </Link>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Listing Review #{approvalData.id}
                </h1>
                <p className="text-sm text-gray-500">
                  Submitted on {formatDate(approvalData.approvalStatusAt)}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {getStatusBadge(approvalData.approvalStatus)}
              {approvalData.approvalStatus === "pending" && (
                <>
                  <Button
                    onClick={() => handleDecision("rejected")}
                    variant="outline"
                    className="text-red-600 border-red-200 hover:bg-red-50"
                  >
                    <XCircle className="w-4 h-4 mr-2" />
                    Reject
                  </Button>
                  <Button
                    onClick={() => handleDecision("approved")}
                    className="bg-[#009639] hover:bg-[#007A2F] text-white"
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Approve
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={currentTab} onValueChange={setCurrentTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="details">Listing Details</TabsTrigger>
            <TabsTrigger value="applicant">Applicant Info</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Main Info Card */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Car className="w-5 h-5 mr-2 text-[#009639]" />
                      {approvalData.sourceType === "vehicle" ? "Vehicle" : "Catalog"} Listing
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-gray-500">Make & Model</p>
                        <p className="text-lg font-semibold">
                          {approvalData.sourceDetails.make} {approvalData.sourceDetails.model}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-500">Year</p>
                        <p className="text-lg font-semibold">{approvalData.sourceDetails.year || "N/A"}</p>
                      </div>
                      {approvalData.sourceDetails.registrationNumber && (
                        <div>
                          <p className="text-sm font-medium text-gray-500">Registration</p>
                          <p className="text-lg font-semibold">{approvalData.sourceDetails.registrationNumber}</p>
                        </div>
                      )}
                      <div>
                        <p className="text-sm font-medium text-gray-500">Listing Type</p>
                        <p className="text-lg font-semibold capitalize">{approvalData.listingType}</p>
                      </div>
                    </div>

                    {/* Listing Details from JSON */}
                    {Object.keys(listingDetails).length > 0 && (
                      <div className="border-t pt-4">
                        <p className="text-sm font-medium text-gray-500 mb-2">Financial Details</p>
                        <div className="grid grid-cols-2 gap-4">
                          {listingDetails.rate && (
                            <div>
                              <p className="text-sm text-gray-500">Rate</p>
                              <p className="font-semibold">R{listingDetails.rate}</p>
                            </div>
                          )}
                          {listingDetails.deposit && (
                            <div>
                              <p className="text-sm text-gray-500">Deposit</p>
                              <p className="font-semibold">R{listingDetails.deposit}</p>
                            </div>
                          )}
                          {listingDetails.fraction && (
                            <div>
                              <p className="text-sm text-gray-500">Fraction</p>
                              <p className="font-semibold">{(listingDetails.fraction * 100).toFixed(1)}%</p>
                            </div>
                          )}
                          {listingDetails.amount && (
                            <div>
                              <p className="text-sm text-gray-500">Amount</p>
                              <p className="font-semibold">R{listingDetails.amount}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Status & Actions Card */}
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <FileText className="w-5 h-5 mr-2 text-[#009639]" />
                      Status & Timeline
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Current Status</p>
                      <div className="mt-1">
                        {getStatusBadge(approvalData.approvalStatus)}
                      </div>
                    </div>
                    
                    <div>
                      <p className="text-sm font-medium text-gray-500">Status Date</p>
                      <p className="text-sm">{formatShortDate(approvalData.approvalStatusAt)}</p>
                    </div>

                    {approvalData.approvalReason && (
                      <div>
                        <p className="text-sm font-medium text-gray-500">Reason</p>
                        <p className="text-sm">{approvalData.approvalReason}</p>
                      </div>
                    )}

                    <div>
                      <p className="text-sm font-medium text-gray-500">Effective Period</p>
                      <p className="text-sm">
                        {formatShortDate(approvalData.effectiveFrom)} - {" "}
                        {approvalData.effectiveTo ? formatShortDate(approvalData.effectiveTo) : "Ongoing"}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="details" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Detailed Listing Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Raw JSON Display */}
                  <div>
                    <h3 className="text-lg font-medium mb-3">Listing Details (JSON)</h3>
                    <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
                      {JSON.stringify(listingDetails, null, 2)}
                    </pre>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="applicant" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="w-5 h-5 mr-2 text-[#009639]" />
                  Applicant Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Name</p>
                    <p className="text-lg font-semibold">{approvalData.partyName || "Unknown"}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Email</p>
                    <p className="text-lg">{approvalData.partyEmail || "Not provided"}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Party ID</p>
                    <p className="text-lg">{approvalData.partyId}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Application History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <FileText className="w-4 h-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium">Application Submitted</p>
                      <p className="text-sm text-gray-500">
                        {formatDate(approvalData.approvalStatusAt)}
                      </p>
                    </div>
                  </div>
                  
                  {approvalData.approvalStatus !== "pending" && (
                    <div className="flex items-start space-x-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        approvalData.approvalStatus === "approved" ? "bg-green-100" : "bg-red-100"
                      }`}>
                        {approvalData.approvalStatus === "approved" ? (
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        ) : (
                          <XCircle className="w-4 h-4 text-red-600" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium">
                          Application {approvalData.approvalStatus === "approved" ? "Approved" : "Rejected"}
                        </p>
                        <p className="text-sm text-gray-500">
                          {formatDate(approvalData.approvalStatusAt)}
                        </p>
                        {approvalData.approvalReason && (
                          <p className="text-sm text-gray-600 mt-1">
                            Reason: {approvalData.approvalReason}
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Decision Dialog */}
      <Dialog open={isDecisionDialogOpen} onOpenChange={setIsDecisionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {pendingDecision === "approved" ? "Approve" : "Reject"} Listing
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to {pendingDecision} this listing application?
              {pendingDecision === "rejected" && " Please provide a reason."}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="reason">
                {pendingDecision === "approved" ? "Approval Notes (Optional)" : "Rejection Reason"}
              </Label>
              <Textarea
                id="reason"
                placeholder={
                  pendingDecision === "approved"
                    ? "Add any notes about this approval..."
                    : "Please explain why this application is being rejected..."
                }
                value={decisionReason}
                onChange={(e) => setDecisionReason(e.target.value)}
                className="mt-1"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDecisionDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={confirmDecision}
              className={
                pendingDecision === "approved"
                  ? "bg-[#009639] hover:bg-[#007A2F]"
                  : "bg-red-600 hover:bg-red-700"
              }
              disabled={pendingDecision === "rejected" && !decisionReason.trim()}
            >
              {pendingDecision === "approved" ? "Approve" : "Reject"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
